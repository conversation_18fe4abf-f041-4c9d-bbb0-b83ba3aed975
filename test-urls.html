<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络地址处理测试</title>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        /* 下载链接样式 */
        a[href*="📥"] {
            display: inline-block;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white !important;
            text-decoration: none !important;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 500;
            margin: 4px 0;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }
        a[href*="📥"]:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
        }
        /* 普通链接样式 */
        a[href*="🔗"] {
            display: inline-block;
            background: linear-gradient(135deg, #2563EB 0%, #1E40AF 100%);
            color: white !important;
            text-decoration: none !important;
            padding: 6px 12px;
            border-radius: 4px;
            font-weight: 500;
            margin: 2px 0;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(37, 99, 235, 0.3);
            font-size: 14px;
        }
        a[href*="🔗"]:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 10px rgba(37, 99, 235, 0.4);
            background: linear-gradient(135deg, #1E40AF 0%, #1E3A8A 100%);
        }
        /* 图片样式 */
        img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <h1>网络地址处理功能测试</h1>
    
    <div class="test-container">
        <h2>测试内容</h2>
        <p>以下是包含各种网络地址的测试文本：</p>
        <div id="test-content">
            这里有一个图片地址：https://example.com/image.jpg

            这里有一个PDF下载地址：https://example.com/document.pdf

            这里有一个普通网址：https://www.google.com

            这里有一个ZIP文件：https://example.com/archive.zip

            这里有一个PNG图片：https://example.com/photo.png

            这里有一个已经是Markdown格式的链接：[现有链接](https://example.com/existing.pdf)

            这里有多个文件类型：
            - Word文档：https://example.com/report.docx
            - Excel表格：https://example.com/data.xlsx
            - 视频文件：https://example.com/video.mp4
            - SVG图片：https://example.com/icon.svg
        </div>
    </div>
    
    <div class="test-container">
        <h2>处理结果</h2>
        <div class="result" id="result"></div>
    </div>

    <script>
        // 复制 processNetworkUrls 函数
        function processNetworkUrls(content) {
            // 图片文件扩展名
            const imageExtensions = /\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i;
            // 下载文件扩展名
            const downloadExtensions = /\.(pdf|doc|docx|xls|xlsx|ppt|pptx|zip|rar|7z|tar|gz|mp4|mp3|avi|mov|txt|csv)$/i;

            // 先处理已经是 Markdown 格式的链接，避免重复处理
            const markdownLinkRegex = /\[([^\]]*)\]\(([^)]+)\)/g;
            const existingLinks = [];
            content.replace(markdownLinkRegex, (match, text, url) => {
                existingLinks.push(url);
                return match;
            });

            // URL 正则表达式 - 匹配不在 Markdown 链接中的 URL
            const urlRegex = /(https?:\/\/[^\s<>"{}|\\^`\[\]()]+)/g;

            return content.replace(urlRegex, (url) => {
                // 移除末尾的标点符号
                const cleanUrl = url.replace(/[.,;:!?]+$/, '');

                // 如果这个 URL 已经在 Markdown 链接中，跳过处理
                if (existingLinks.includes(cleanUrl)) {
                    return url;
                }

                if (imageExtensions.test(cleanUrl)) {
                    // 如果是图片地址，生成图片标签
                    return `![图片](${cleanUrl})`;
                } else if (downloadExtensions.test(cleanUrl)) {
                    // 如果是下载文件地址，生成下载按钮
                    const fileName = cleanUrl.split('/').pop() || '下载文件';
                    return `[📥 下载 ${fileName}](${cleanUrl})`;
                } else {
                    // 其他网络地址保持原样，但转换为链接格式
                    return `[🔗 ${cleanUrl}](${cleanUrl})`;
                }
            });
        }

        // 测试函数
        function runTest() {
            const testContent = document.getElementById('test-content').textContent;
            const processedContent = processNetworkUrls(testContent);
            const html = marked.parse(processedContent);
            document.getElementById('result').innerHTML = html;
        }

        // 页面加载后运行测试
        window.onload = runTest;
    </script>
</body>
</html>
