<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="utf-8" />
  <title>微信式 Coze 聊天 - 完美 Markdown 版</title>
  <link rel="stylesheet" href="style.css" />
  <link rel="stylesheet" href="markdown-style.css" />
  <!-- Markdown 渲染库 -->
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/lib/common.min.js"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/styles/github.min.css" />
  <!-- 新增升级样式 -->
  <style>
    /* 配色方案 */
    :root {
      --primary-color: #2563EB;
      --secondary-color: #60A5FA;
      --bg-color: #F8FAFC;
      --secondary-bg: #E2E8F0;
      --text-color: #1E293B;
      --text-secondary: #64748B;
    }

    /* Markdown 容器升级 */
    .markdown-container {
      max-width: 680px;
      margin: 0 auto;
      padding: 24px;
      background-color: var(--bg-color);
      font-family: "Helvetica Neue", Inter, sans-serif;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
    }

    /* 标题样式升级 */
    .markdown-container h1 {
      font-size: 28px;
      font-weight: 700;
      border-left: 4px solid var(--primary-color);
      padding-left: 12px;
      margin-top: 32px;
      color: var(--text-color);
    }

    .markdown-container h2 {
      font-size: 22px;
      margin-top: 24px;
      color: var(--primary-color);
    }

    /* 正文段落升级 */
    .markdown-container p {
      font-size: 16px;
      line-height: 1.8;
      margin-bottom: 16px;
      color: var(--text-color);
    }

    /* 列表升级 */
    .markdown-container ul {
      padding-left: 24px;
      list-style-type: disc;
    }

    .markdown-container li {
      margin-bottom: 8px;
    }

    /* 引用和代码块升级 */
    .markdown-container blockquote {
      border-left: 4px solid var(--secondary-color);
      background: #EFF6FF;
      padding: 12px 16px;
      color: #334155;
      font-style: italic;
      border-radius: 4px;
    }

    .markdown-container pre {
      background: var(--secondary-bg);
      padding: 16px;
      border-radius: 6px;
      overflow-x: auto;
      font-family: 'Courier New', monospace;
    }

    /* 骨架屏样式 */
    .markdown-loading-skeleton {
      padding: 20px;
      background: var(--bg-color);
      border-radius: 8px;
    }

    .skeleton {
      background: linear-gradient(100deg, #f1f5f9 8%, #e2e8f0 18%, #f1f5f9 33%);
      background-size: 1200px 100%;
      animation: shimmer 1.5s infinite linear;
      border-radius: 4px;
      margin-bottom: 12px;
    }

    .skeleton.title {
      height: 28px;
      width: 50%;
    }

    .skeleton.line {
      height: 16px;
      width: 100%;
    }

    .skeleton.line.short {
      width: 70%;
    }

    @keyframes shimmer {
      0% {
        background-position: -1200px 0;
      }

      100% {
        background-position: 1200px 0;
      }
    }

    /* 加载提示动画 */
    .loader-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 32px;
      font-size: 16px;
      color: var(--primary-color);
    }

    .dot-loader span {
      animation: blink 1.4s infinite;
      font-size: 24px;
      margin: 0 2px;
      color: var(--primary-color);
    }

    .dot-loader span:nth-child(2) {
      animation-delay: 0.2s;
    }

    .dot-loader span:nth-child(3) {
      animation-delay: 0.4s;
    }

    @keyframes blink {

      0%,
      80%,
      100% {
        opacity: 0;
      }

      40% {
        opacity: 1;
      }
    }

    /* 错误提示样式 */
    .error-message {
      background-color: #FEE2E2;
      border-left: 4px solid #EF4444;
      color: #B91C1C;
      padding: 16px;
      margin: 16px 0;
      border-radius: 4px;
    }

    /* 存智功能样式 */
    .cunzhi-container {
      display: flex;
      justify-content: space-between;
      margin-top: 12px;
      gap: 8px;
    }

    .cunzhi-btn {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      padding: 8px 12px;
      background: linear-gradient(135deg, var(--primary-color) 0%, #1E40AF 100%);
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 13px;
      transition: all 0.3s ease;
    }

    .cunzhi-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
    }

    .cunzhi-icon {
      font-size: 16px;
    }

    /* 适配 markdown-body 到新的样式 */
    .message-ai .markdown-body {
      max-width: 680px;
      font-family: "Helvetica Neue", Inter, sans-serif;
    }

    .message-ai .markdown-body h1 {
      font-size: 28px;
      font-weight: 700;
      border-left: 4px solid var(--primary-color);
      padding-left: 12px;
      margin-top: 24px;
      color: var(--text-color);
    }

    .message-ai .markdown-body h2 {
      font-size: 22px;
      margin-top: 20px;
      color: var(--primary-color);
    }

    /* 改进消息项样式 */
    .message-item {
      transition: all 0.3s ease;
    }

    .message-user {
      background: linear-gradient(135deg, var(--primary-color) 0%, #1E40AF 100%);
      box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
    }

    .message-ai {
      background: var(--bg-color);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    /* 下载链接样式 */
    .markdown-container a[href*="📥"] {
      display: inline-block;
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      color: white !important;
      text-decoration: none !important;
      padding: 8px 16px;
      border-radius: 6px;
      font-weight: 500;
      margin: 4px 0;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
    }

    .markdown-container a[href*="📥"]:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
      background: linear-gradient(135deg, #059669 0%, #047857 100%);
    }

    /* 普通链接样式 */
    .markdown-container a[href*="🔗"] {
      display: inline-block;
      background: linear-gradient(135deg, var(--primary-color) 0%, #1E40AF 100%);
      color: white !important;
      text-decoration: none !important;
      padding: 6px 12px;
      border-radius: 4px;
      font-weight: 500;
      margin: 2px 0;
      transition: all 0.3s ease;
      box-shadow: 0 2px 6px rgba(37, 99, 235, 0.3);
      font-size: 14px;
    }

    .markdown-container a[href*="🔗"]:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 10px rgba(37, 99, 235, 0.4);
      background: linear-gradient(135deg, #1E40AF 0%, #1E3A8A 100%);
    }

    /* 图片样式优化 */
    .markdown-container img {
      max-width: 100%;
      height: auto;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      margin: 8px 0;
      transition: all 0.3s ease;
    }

    .markdown-container img:hover {
      transform: scale(1.02);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }
  </style>
  <!-- Coze SDK -->
  <script src="https://lf-cdn.coze.cn/obj/unpkg/flow-platform/chat-app-sdk/1.2.0-beta.10/libs/cn/index.js"></script>
  <style>
    /* 自定义聊天界面样式 */
    /* 添加错误震动动画 */
    @keyframes shake-error {

      0%,
      100% {
        transform: translateX(0);
      }

      10%,
      30%,
      50%,
      70%,
      90% {
        transform: translateX(-5px);
      }

      20%,
      40%,
      60%,
      80% {
        transform: translateX(5px);
      }
    }

    .shake-error {
      animation: shake-error 0.5s cubic-bezier(.36, .07, .19, .97) both;
    }

    .chat-container {
      display: flex;
      height: 100vh;
      background: #f5f8ff;
    }

    .agent-sidebar {
      width: 260px;
      background: white;
      border-right: 1px solid #e0e0e0;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
    }

    .sidebar-header {
      padding: 20px;
      border-bottom: 1px solid #f0f0f0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      text-align: center;
    }

    .sidebar-header h3 {
      margin: 0 0 10px 0;
      font-size: 18px;
    }

    .sidebar-header p {
      margin: 0;
      font-size: 12px;
      opacity: 0.9;
    }

    .agent-list {
      flex: 1;
      padding: 15px;
    }
    
    .agent-category-title {
      font-size: 14px;
      font-weight: 600;
      color: #64748B;
      margin: 10px 0;
      padding: 5px 10px;
      background: #f1f5f9;
      border-radius: 4px;
    }
    
    .empty-agents-message {
      padding: 20px;
      text-align: center;
      color: #64748B;
      font-style: italic;
    }
    


    .agent-item {
      display: flex;
      padding: 12px;
      cursor: pointer;
      border-radius: 8px;
      margin-bottom: 8px;
      transition: all 0.3s ease;
      border: 1px solid transparent;
    }

    .agent-item:hover {
      background-color: #f0f8ff;
      border-color: #e0e7ff;
    }

    .agent-item.selected {
      background: linear-gradient(135deg, #e6f3ff 0%, #f0f8ff 100%);
      border-color: #3b82f6;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
      transform: translateX(3px);
      position: relative;
    }

    .agent-item.selected::after {
      content: "●";
      position: absolute;
      right: 15px;
      top: 50%;
      transform: translateY(-50%);
      color: #10b981;
      font-size: 12px;
      animation: pulse 2s infinite;
    }

    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-right: 12px;
      border: 2px solid #f1f5f9;
      transition: border-color 0.3s ease;
    }

    .agent-item.selected .avatar {
      border-color: #3b82f6;
    }

    .agent-info {
      flex: 1;
      min-width: 0;
    }

    .name {
      font-weight: 600;
      font-size: 14px;
      color: #1f2937;
      margin-bottom: 4px;
    }

    .desc {
      font-size: 12px;
      color: #6b7280;
      line-height: 1.3;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .agent-item.selected .name {
      color: #3b82f6;
      font-weight: 700;
    }

    .agent-item.selected .desc {
      color: #6366f1;
    }

    .sidebar-footer {
      padding: 15px;
      border-top: 1px solid #f0f0f0;
      background: #f8f9fa;
      text-align: center;
      font-size: 14px;
      color: #374151;
    }

    .current-agent-display {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 8px 12px;
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      border-radius: 8px;
      border: 1px solid #bae6fd;
    }

    .agent-indicator {
      color: #10b981;
      font-size: 16px;
      animation: pulse 2s infinite;
    }

    #current-agent {
      font-weight: 600;
      color: #0369a1;
    }

    @keyframes pulse {

      0%,
      100% {
        opacity: 1;
      }

      50% {
        opacity: 0.5;
      }
    }

    .chat-area {
      flex: 1;
      display: flex;
      flex-direction: column;
      background: white;
    }

    .chat-header {
      padding: 15px 20px;
      background: #f0f8ff;
      border-bottom: 1px solid #e0e7ff;
      font-size: 14px;
      color: #3b82f6;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .messages-container {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      background: #ffffff;
    }

    .input-area {
      border-top: 1px solid #e0e0e0;
      padding: 20px;
      background: #f8f9fa;
    }

    .input-container {
      display: flex;
      gap: 12px;
      align-items: flex-end;
    }

    .user-avatar-container {
      flex-shrink: 0;
    }

    .user-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: 2px solid #e5e7eb;
      object-fit: cover;
      transition: all 0.3s ease;
    }

    .user-avatar:hover {
      border-color: #3b82f6;
      transform: scale(1.05);
    }

    .user-input {
      flex: 1;
      min-height: 60px;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 8px;
      font-size: 14px;
      resize: vertical;
      font-family: inherit;
      outline: none;
      transition: border-color 0.3s ease;
    }

    .send-btn {
      margin-top: 10px;
      padding: 10px 20px;
      background: #3b82f6;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
    }

    .send-btn:hover {
      background: #2563eb;
    }

    .send-btn:disabled {
      background: #9ca3af;
      cursor: not-allowed;
    }

    .message-item {
      margin-bottom: 20px;
      display: flex;
      align-items: flex-start;
      gap: 12px;
    }

    .message-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      flex-shrink: 0;
      border: 2px solid #f1f5f9;
      object-fit: cover;
    }

    .message-content {
      padding: 15px;
      border-radius: 12px;
      position: relative;
    }

    .message-user {
      margin-left: auto;
      flex-direction: row-reverse;
      max-width: 75%;
    }

    .message-user .message-content {
      background: #3b82f6;
      color: white;
      text-align: right;
      border-top-right-radius: 0;
    }

    .message-user .message-avatar {
      border-color: #3b82f6;
    }

    .message-ai {
      max-width: 85%;
    }

    .message-ai .message-content {
      background: #f1f5f9;
      color: #1e293b;
      border-top-left-radius: 0;
    }

    .message-ai .message-avatar {
      border-color: #f1f5f9;
    }

    .message-ai .markdown-body {
      background: transparent;
      padding: 0;
      margin: 0;
      box-shadow: none;
    }

    .loading-indicator {
      display: flex;
      align-items: center;
      gap: 10px;
      color: #6b7280;
      font-style: italic;
    }

    .loading-dots::after {
      content: '';
      animation: dots 1.5s steps(4, end) infinite;
    }

    @keyframes dots {

      0%,
      20% {
        content: '';
      }

      40% {
        content: '.';
      }

      60% {
        content: '..';
      }

      80%,
      100% {
        content: '...';
      }
    }
  </style>
</head>

<body>
  <div class="chat-container">
    <!-- 左侧智能体列表 -->
    <div class="agent-sidebar">
      <div class="sidebar-header">
        <h3>🤖 AI 智能助手</h3>
        <p>选择您需要的助手类型</p>
      </div>

      <div class="agent-list" id="agent-list">
        <!-- 智能体列表将在这里动态生成 -->
      </div>
      


      <div class="sidebar-footer">
        <div class="current-agent-display">
          <span class="agent-indicator">●</span>
          <span id="current-agent">Coze助手</span>
        </div>
      </div>
    </div>

    <!-- 右侧聊天区域 -->
    <div class="chat-area">
      <div class="chat-header">
        <span>💬 与 <span id="chat-agent-name">Coze助手</span> 对话</span>
      </div>

      <div class="messages-container" id="messages-container">
        <!-- 消息将在这里显示 -->
      </div>

      <div class="input-area">
        <div class="input-container">
          <div class="user-avatar-container">
            <img src="touxiang/3.png" class="user-avatar" alt="用户头像" />
          </div>
          <textarea id="user-input" class="user-input" placeholder="请输入您的问题..."
            onkeydown="handleKeyDown(event)"></textarea>
          <button id="send-btn" class="send-btn" onclick="sendMessage()">
            发送消息
          </button>
        </div>
      </div>
    </div>
  </div>

  <script src="main.js"></script>
</body>

</html>