<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能体配置测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>智能体配置测试</h1>
        
        <div class="test-section">
            <h3>1. 检查 localStorage</h3>
            <button onclick="checkLocalStorage()">检查存储</button>
            <button onclick="clearLocalStorage()">清除存储</button>
            <div id="localStorage-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. 测试智能体配置函数</h3>
            <button onclick="testAgentConfig()">测试配置</button>
            <div id="config-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. 创建默认配置</h3>
            <button onclick="createDefaultConfig()">创建默认配置</button>
            <div id="default-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 测试主页面</h3>
            <button onclick="openMainPage()">打开主页面</button>
            <div id="main-result" class="result">点击按钮打开主页面进行测试</div>
        </div>
    </div>

    <script>
        // 复制主要的配置函数
        function generateId() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
        }

        function getAgentConfigs() {
            const STORAGE_KEY = 'coze_agents_config';
            
            const defaultAgents = [
                {
                    id: generateId(),
                    name: 'Coze助手',
                    description: '智能AI助手，为您答疑解惑',
                    type: 'employee',
                    bot_id: '7523880023146168366',
                    avatar: 'touxiang/11.png'
                },
                {
                    id: generateId(),
                    name: '智能客服',
                    description: '专业的客服助手',
                    type: 'employee',
                    bot_id: '7523880023146168366',
                    avatar: 'touxiang/12.png'
                },
                {
                    id: generateId(),
                    name: '学习助手',
                    description: '帮助您学习和成长',
                    type: 'tool',
                    bot_id: '7523880023146168366',
                    avatar: 'touxiang/13.png'
                },
                {
                    id: generateId(),
                    name: '编程助手',
                    description: '代码编写和技术问题',
                    type: 'tool',
                    bot_id: '7523880023146168366',
                    avatar: 'touxiang/16.png'
                }
            ];

            let agents = [];
            
            try {
                const agentsJson = localStorage.getItem(STORAGE_KEY);
                agents = agentsJson ? JSON.parse(agentsJson) : [];
            } catch (error) {
                console.warn('读取智能体配置失败，使用默认配置:', error);
                agents = [];
            }
            
            if (!agents || agents.length === 0) {
                agents = defaultAgents;
                
                try {
                    localStorage.setItem(STORAGE_KEY, JSON.stringify(agents));
                    console.log('已保存默认智能体配置到 localStorage');
                } catch (error) {
                    console.warn('保存智能体配置失败:', error);
                }
            }
            
            const configs = {};
            agents.forEach(agent => {
                configs[agent.name] = {
                    bot_id: agent.bot_id,
                    description: agent.description,
                    avatar: agent.avatar,
                    type: agent.type
                };
            });
            
            return configs;
        }

        function checkLocalStorage() {
            const result = document.getElementById('localStorage-result');
            try {
                const data = localStorage.getItem('coze_agents_config');
                if (data) {
                    const parsed = JSON.parse(data);
                    result.textContent = `存储数据存在，包含 ${parsed.length} 个智能体:\n${JSON.stringify(parsed, null, 2)}`;
                    result.className = 'result success';
                } else {
                    result.textContent = '存储数据不存在';
                    result.className = 'result error';
                }
            } catch (error) {
                result.textContent = `检查存储失败: ${error.message}`;
                result.className = 'result error';
            }
        }

        function clearLocalStorage() {
            localStorage.removeItem('coze_agents_config');
            document.getElementById('localStorage-result').textContent = '存储已清除';
            document.getElementById('localStorage-result').className = 'result';
        }

        function testAgentConfig() {
            const result = document.getElementById('config-result');
            try {
                const configs = getAgentConfigs();
                const agentNames = Object.keys(configs);
                result.textContent = `配置函数正常，获取到 ${agentNames.length} 个智能体:\n${agentNames.join(', ')}\n\n详细配置:\n${JSON.stringify(configs, null, 2)}`;
                result.className = 'result success';
            } catch (error) {
                result.textContent = `配置函数失败: ${error.message}`;
                result.className = 'result error';
            }
        }

        function createDefaultConfig() {
            const result = document.getElementById('default-result');
            try {
                // 清除现有配置
                localStorage.removeItem('coze_agents_config');
                
                // 创建新配置
                const configs = getAgentConfigs();
                const agentNames = Object.keys(configs);
                
                result.textContent = `默认配置创建成功，包含 ${agentNames.length} 个智能体:\n${agentNames.join(', ')}`;
                result.className = 'result success';
            } catch (error) {
                result.textContent = `创建默认配置失败: ${error.message}`;
                result.className = 'result error';
            }
        }

        function openMainPage() {
            window.open('index.html', '_blank');
        }

        // 页面加载时自动检查
        window.onload = function() {
            checkLocalStorage();
        };
    </script>
</body>
</html>
