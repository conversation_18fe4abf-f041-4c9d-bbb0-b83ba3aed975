// Coze API 配置
const COZE_CONFIG = {
  bot_id: '7523880023146168366',
  token: 'sat_sWfwyuKNho8ZHtkm2nmiXsXlFntAptsaa9XC6HpofAA3XLpTRoS5gGmkCJ2LpwaP',
  api_base: 'https://api.coze.cn/open_api/v2',
};

let currentAgent = {
  name: '',
  bot_id: COZE_CONFIG.bot_id
};
let isLoading = false;
let conversationId = null; // 用于维持对话上下文

// 存储所有智能体的聊天记录
let chatHistories = {};

// 处理网络地址的函数
function processNetworkUrls(content) {
  // 图片文件扩展名
  const imageExtensions = /\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i;
  // 下载文件扩展名
  const downloadExtensions = /\.(pdf|doc|docx|xls|xlsx|ppt|pptx|zip|rar|7z|tar|gz|mp4|mp3|avi|mov|txt|csv)$/i;

  // 先处理已经是 Markdown 格式的链接，避免重复处理
  const markdownLinkRegex = /\[([^\]]*)\]\(([^)]+)\)/g;
  const existingLinks = [];
  content.replace(markdownLinkRegex, (match, text, url) => {
    existingLinks.push(url);
    return match;
  });

  // URL 正则表达式 - 匹配不在 Markdown 链接中的 URL
  const urlRegex = /(https?:\/\/[^\s<>"{}|\\^`\[\]()]+)/g;

  return content.replace(urlRegex, (url) => {
    // 移除末尾的标点符号
    const cleanUrl = url.replace(/[.,;:!?]+$/, '');

    // 如果这个 URL 已经在 Markdown 链接中，跳过处理
    if (existingLinks.includes(cleanUrl)) {
      return url;
    }

    if (imageExtensions.test(cleanUrl)) {
      // 如果是图片地址，生成图片标签
      return `![图片](${cleanUrl})`;
    } else if (downloadExtensions.test(cleanUrl)) {
      // 如果是下载文件地址，生成下载按钮
      const fileName = cleanUrl.split('/').pop() || '下载文件';
      return `[📥 下载 ${fileName}](${cleanUrl})`;
    } else {
      // 其他网络地址保持原样，但转换为链接格式
      return `[🔗 ${cleanUrl}](${cleanUrl})`;
    }
  });
}

// Markdown 渲染配置
if (typeof marked !== 'undefined') {
  marked.setOptions({
    highlight: function(code, lang) {
      if (typeof hljs !== 'undefined' && lang && hljs.getLanguage(lang)) {
        try {
          return hljs.highlight(code, { language: lang }).value;
        } catch (err) {}
      }
      return code;
    },
    breaks: true,
    gfm: true
  });
}

// 调用 Coze API
async function callCozeAPI(message, botId = COZE_CONFIG.bot_id) {
  try {
    console.log('调用 Coze API:', { message, botId, conversationId });

    const requestBody = {
      bot_id: botId,
      user: "user_" + Date.now(),
      query: message,
      chat_history: [],
      stream: false // 禁用流式输出
    };

    // 如果有对话ID，添加到请求中
    if (conversationId) {
      requestBody.conversation_id = conversationId;
    }

    const response = await fetch(`${COZE_CONFIG.api_base}/chat`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${COZE_CONFIG.token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`API 调用失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('API 响应:', data);

    // 保存对话ID用于后续对话
    if (data.conversation_id) {
      conversationId = data.conversation_id;
    }

    // 提取回复内容
    if (data.messages && data.messages.length > 0) {
      // 找到最后一条 assistant 消息
      const assistantMessage = data.messages
        .filter(msg => msg.role === 'assistant' && msg.type === 'answer')
        .pop();

      if (assistantMessage && assistantMessage.content) {
        return assistantMessage.content;
      }
    }

    // 如果没有找到合适的回复，返回默认消息
    return '抱歉，我暂时无法回答您的问题，请稍后再试。';

  } catch (error) {
    console.error('Coze API 调用错误:', error);

    // 根据错误类型返回不同的错误消息
    if (error.message.includes('401')) {
      return '❌ **认证失败**\n\n请检查 API Token 是否正确。';
    } else if (error.message.includes('403')) {
      return '❌ **权限不足**\n\n请检查 Token 权限或 Bot ID 是否正确。';
    } else if (error.message.includes('429')) {
      return '❌ **请求过于频繁**\n\n请稍后再试。';
    } else if (error.message.includes('500')) {
      return '❌ **服务器错误**\n\n服务暂时不可用，请稍后再试。';
    } else {
      return `❌ **网络错误**\n\n${error.message}\n\n请检查网络连接后重试。`;
    }
  }
}

// Markdown 渲染配置
if (typeof marked !== 'undefined') {
  marked.setOptions({
    highlight: function(code, lang) {
      if (typeof hljs !== 'undefined' && lang && hljs.getLanguage(lang)) {
        try {
          return hljs.highlight(code, { language: lang }).value;
        } catch (err) {}
      }
      return code;
    },
    breaks: true,
    gfm: true
  });
}

// 从配置文件获取智能体配置
async function getAgentConfigs() {
  console.log('从配置文件获取智能体配置...');

  try {
    const response = await fetch('agents-config.json');
    if (!response.ok) {
      throw new Error(`配置文件加载失败: ${response.status}`);
    }

    const configData = await response.json();
    console.log('从配置文件读取到数据:', configData);

    // 转换为所需的格式
    const configs = {};
    if (configData.agents && Array.isArray(configData.agents)) {
      // 创建分类映射
      const categoryMap = {};
      if (configData.categories && Array.isArray(configData.categories)) {
        configData.categories.forEach(category => {
          categoryMap[category.id] = category.name;
        });
      }

      configData.agents.forEach(agent => {
        // 根据分类 ID 确定类型
        let type = 'employee'; // 默认类型
        const categoryName = categoryMap[agent.category];
        if (categoryName) {
          if (categoryName.includes('工具') || categoryName.includes('tool')) {
            type = 'tool';
          } else if (categoryName.includes('员工') || categoryName.includes('employee')) {
            type = 'employee';
          }
        }

        configs[agent.name] = {
          bot_id: agent.bot_id,
          description: agent.description,
          avatar: agent.avatar,
          type: type,
          greeting: agent.greeting,
          categoryId: agent.category,
          categoryName: categoryName
        };
      });
    }

    console.log('智能体配置已加载:', configs);
    return configs;

  } catch (error) {
    console.error('从配置文件读取失败:', error);

    // 如果配置文件读取失败，返回默认配置
    const defaultConfigs = {
      'Coze助手': {
        bot_id: '7523880023146168366',
        description: '智能AI助手，为您答疑解惑',
        avatar: 'touxiang/11.png',
        type: 'employee',
        greeting: '👋 您好！我是 Coze助手\n\n我已经准备好为您服务了！'
      },
      '公文写作专家': {
        bot_id: '7528255758204076072',
        description: '帮你写公文',
        avatar: 'touxiang/12.png',
        type: 'tool',
        greeting: '你好呀～📝\n我是你的公文写作助理，帮你把一篇普通的文字变成精准、得体、专业的公文。\n为了更好地帮你构思和落笔，请提供以下信息（越完整越好）：\n1️⃣ 公文类型：比如请示、通知、报告、函件、纪要等\n2️⃣ 写作主题：主要围绕什么内容？\n3️⃣ 核心要点 / 背景信息：希望传达哪些关键信息？是否有具体数据或内容？\n4️⃣ 使用场景：比如向上汇报、对外沟通、内部传达等\n⚠️温馨提醒：\n如果信息不够完整，我可能无法精准还原你期望的效果哦～可以多提供一些上下文，让我们一起把这篇公文写得漂亮又实用！'
      },
      '日报专家': {
        bot_id: '7528256246898622518',
        description: '帮你写日报',
        avatar: 'touxiang/13.png',
        type: 'tool',
        greeting: '您好呀， 我是您的专属日报生成助手，擅长将零散信息整理成结构清晰、表达精准的工作日报！\n为了帮您高效生成日报，请提供以下信息👇：\n1️⃣ 日报日期：例如 2025 年 7 月 19 日\n2️⃣ 您的姓名：方便标识提交人\n3️⃣ 所在部门：如产品部、运营部、技术部等\n4️⃣ 今日核心工作内容：完成了哪些主要事项？是否有阶段性成果？\n5️⃣ 遇到的问题及解决方案（如有）：简单描述问题及应对策略\n6️⃣ 明日工作计划：预计完成哪些任务？是否有关键节点？\n⚠️ 温馨提醒：\n提供的信息越完整，我生成的日报就越规范、越贴合您的实际工作内容'
      },
      '小红书图文总结专家': {
        bot_id: '7524620947187056679',
        description: '帮你总结小红书图文',
        avatar: 'touxiang/16.png',
        type: 'employee',
        greeting: '您好呀，欢迎来到📕小红书图文信息提取助手～\n我随时为您提取小红书图文内容并整理格式，让信息更加清晰有条理！\n\n请按照以下步骤操作：\n\n📌 选择您想提取的小红书图文\n➡️ 点击右上角【分享】按钮\n➡️ 选择【复制链接】，并直接粘贴给我\n\n✅ 正确的格式如下（请勿修改内容）：\n\n64 【非常高的待遇！湖南长沙民营三甲医院，副主 - 老庄聊薪水 | 小红书 - 你的生活兴趣社区】\n😆 lCN0bkG750uX9g1 😆\nhttps://www.xiaohongshu.com/discovery/item/687122f300000000130105ca?source=webshare&xhsshare=pc_web&xsec_token=ABEyFMTH91grJ5xhrYMTppN2P3hkF8f09s2MzoU_Oovvw=&xsec_source=pc_share\n⚠️ 温馨提醒：\n请完整复制链接内容（包括编号、标题、链接和口令），这样我才能准确提取图文信息并为您整理哦～'
      }
    };

    console.log('使用默认配置:', defaultConfigs);
    return defaultConfigs;
  }
}

// 生成唯一ID
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
}

// 智能体配置 - 动态获取，避免初始化时的问题
let AGENT_CONFIGS = null;

// 获取智能体配置（带缓存）- 异步版本
async function getAgentConfigsCached() {
  if (!AGENT_CONFIGS) {
    AGENT_CONFIGS = await getAgentConfigs();
  }
  return AGENT_CONFIGS;
}

// 重置智能体配置缓存
function resetAgentConfigsCache() {
  AGENT_CONFIGS = null;
}

// 智能体选择功能
async function selectAgent(element, agentName) {
  // 移除所有选中状态
  document.querySelectorAll('.agent-item').forEach(item => {
    item.classList.remove('selected');
  });

  // 添加选中状态
  element.classList.add('selected');

  // 更新当前智能体信息
  const agentConfigs = await getAgentConfigsCached();
  const agentConfig = agentConfigs[agentName] || agentConfigs['Coze助手'];
  currentAgent.name = agentName;
  currentAgent.bot_id = agentConfig.bot_id;

  document.getElementById('current-agent').textContent = agentName;
  document.getElementById('chat-agent-name').textContent = agentName;

  // 重置对话ID（切换智能体时开始新对话）
  conversationId = null;

  // 清空聊天容器，准备显示历史记录
  const container = document.getElementById('messages-container');
  container.innerHTML = '';

  // 初始化该智能体的聊天记录（如果不存在）
  if (!chatHistories[agentName]) {
    chatHistories[agentName] = [];
  }

  // 检查是否有历史聊天记录
  if (chatHistories[agentName].length === 0) {
    // 如果没有历史记录，显示欢迎消息
    const welcomeMessage = await getAgentWelcomeMessage(agentName);

    // 直接添加到DOM，不保存到历史记录中
    await displayMessage(welcomeMessage, 'ai', container);
    
    // 保存欢迎消息到历史记录
    chatHistories[agentName].push({
      content: welcomeMessage,
      type: 'ai',
      timestamp: new Date().getTime()
    });
  } else {
    // 如果有历史记录，显示所有历史消息
    for (const msg of chatHistories[agentName]) {
      await displayMessage(msg.content, msg.type, container);
    }
  }

  console.log(`切换到智能体: ${agentName} (Bot ID: ${agentConfig.bot_id})`);
}

// 显示消息到界面，但不保存到历史记录
async function displayMessage(content, type, container) {
  const messageDiv = document.createElement('div');
  messageDiv.className = `message-item message-${type}`;
  
  // 添加头像
  const avatarImg = document.createElement('img');
  avatarImg.className = 'message-avatar';
  
  if (type === 'user') {
    // 用户头像 - 使用输入区域的头像
    avatarImg.src = document.querySelector('.user-avatar').src;
    avatarImg.alt = '用户头像';
    
    // 创建消息内容容器
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.textContent = content;
    
    // 添加到消息项
    messageDiv.appendChild(avatarImg);
    messageDiv.appendChild(contentDiv);
  } else {
    // AI 头像 - 使用当前智能体的头像
    const agentConfigs = await getAgentConfigsCached();
    const agentConfig = agentConfigs[currentAgent.name];
    avatarImg.src = agentConfig ? agentConfig.avatar : 'touxiang/11.png';
    avatarImg.alt = `${currentAgent.name} 头像`;
    
    // 创建消息内容容器
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    
    // AI 消息，渲染 Markdown 并处理网络地址
    const processedContent = processNetworkUrls(content);
    const html = marked.parse(processedContent);

    // 使用新的 markdown-container 类包装内容
    contentDiv.innerHTML = `<div class="markdown-container markdown-body">${html}</div>`;
    
    // 添加到消息项
    messageDiv.appendChild(avatarImg);
    messageDiv.appendChild(contentDiv);

    // 高亮代码块
    contentDiv.querySelectorAll('pre code').forEach((block) => {
      hljs.highlightElement(block);
      
      // 添加代码块复制按钮
      const pre = block.parentElement;
      const copyBtn = document.createElement('button');
      copyBtn.className = 'copy-code-btn';
      copyBtn.innerHTML = '复制';
      copyBtn.onclick = function() {
        const code = block.textContent;
        navigator.clipboard.writeText(code).then(() => {
          copyBtn.innerHTML = '已复制!';
          setTimeout(() => {
            copyBtn.innerHTML = '复制';
          }, 2000);
        });
      };
      pre.style.position = 'relative';
      copyBtn.style.position = 'absolute';
      copyBtn.style.top = '5px';
      copyBtn.style.right = '5px';
      copyBtn.style.padding = '3px 8px';
      copyBtn.style.background = 'rgba(0,0,0,0.1)';
      copyBtn.style.border = 'none';
      copyBtn.style.borderRadius = '4px';
      copyBtn.style.fontSize = '12px';
      copyBtn.style.cursor = 'pointer';
      pre.appendChild(copyBtn);
    });
    
    // 为表格添加样式
    contentDiv.querySelectorAll('table').forEach((table) => {
      table.style.width = '100%';
      table.style.borderCollapse = 'collapse';
      table.style.marginBottom = '16px';
      table.style.overflow = 'hidden';
      table.style.borderRadius = '8px';
      table.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.05)';
      
      // 美化表头
      const headers = table.querySelectorAll('th');
      headers.forEach(header => {
        header.style.backgroundColor = '#2563EB';
        header.style.color = 'white';
        header.style.padding = '10px 16px';
        header.style.textAlign = 'left';
        header.style.fontWeight = '600';
      });
      
      // 美化表格单元格
      const cells = table.querySelectorAll('td');
      cells.forEach(cell => {
        cell.style.padding = '10px 16px';
        cell.style.borderTop = '1px solid #E2E8F0';
      });
      
      // 添加斑马纹
      const rows = table.querySelectorAll('tr:not(:first-child)');
      rows.forEach((row, index) => {
        if (index % 2 === 0) {
          row.style.backgroundColor = '#F8FAFC';
        }
      });
    });
  }

  container.appendChild(messageDiv);

  // 滚动到底部
  container.scrollTop = container.scrollHeight;
}

// 聊天功能
function handleKeyDown(event) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
}

async function sendMessage() {
  if (isLoading) return;

  const input = document.getElementById('user-input');
  const message = input.value.trim();

  if (!message) return;

  // 添加用户消息
  await addMessage(message, 'user');

  // 清空输入框
  input.value = '';

  // 显示加载状态
  showLoading();

  // 调用真实的 AI API
  callRealAI(message);
}

async function addMessage(content, type) {
  const container = document.getElementById('messages-container');
  const messageDiv = document.createElement('div');
  messageDiv.className = `message-item message-${type}`;
  
  // 添加进场动画类
  messageDiv.style.opacity = '0';
  messageDiv.style.transform = 'translateY(20px)';

  // 创建消息对象
  const messageObj = {
    content: content,
    type: type,
    timestamp: new Date().getTime()
  };
  
  // 保存消息到当前智能体的聊天历史
  chatHistories[currentAgent.name].push(messageObj);

  // 添加头像
  const avatarImg = document.createElement('img');
  avatarImg.className = 'message-avatar';
  
  if (type === 'user') {
    // 用户头像 - 使用输入区域的头像
    avatarImg.src = document.querySelector('.user-avatar').src;
    avatarImg.alt = '用户头像';
    
    // 创建消息内容容器
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.textContent = content;
    
    // 添加到消息项
    messageDiv.appendChild(avatarImg);
    messageDiv.appendChild(contentDiv);
  } else {
    // AI 头像 - 使用当前智能体的头像
    const agentConfigs = await getAgentConfigsCached();
    const agentConfig = agentConfigs[currentAgent.name];
    avatarImg.src = agentConfig ? agentConfig.avatar : 'touxiang/11.png';
    avatarImg.alt = `${currentAgent.name} 头像`;
    
    // 创建消息内容容器
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    
    // AI 消息，渲染 Markdown 并处理网络地址
    const processedContent = processNetworkUrls(content);
    const html = marked.parse(processedContent);

    // 使用新的 markdown-container 类包装内容
    contentDiv.innerHTML = `<div class="markdown-container markdown-body">${html}</div>`;
    
    // 添加到消息项
    messageDiv.appendChild(avatarImg);
    messageDiv.appendChild(contentDiv);

    // 高亮代码块
    contentDiv.querySelectorAll('pre code').forEach((block) => {
      hljs.highlightElement(block);
      
      // 添加代码块复制按钮
      const pre = block.parentElement;
      const copyBtn = document.createElement('button');
      copyBtn.className = 'copy-code-btn';
      copyBtn.innerHTML = '复制';
      copyBtn.onclick = function() {
        const code = block.textContent;
        navigator.clipboard.writeText(code).then(() => {
          copyBtn.innerHTML = '已复制!';
          setTimeout(() => {
            copyBtn.innerHTML = '复制';
          }, 2000);
        });
      };
      pre.style.position = 'relative';
      copyBtn.style.position = 'absolute';
      copyBtn.style.top = '5px';
      copyBtn.style.right = '5px';
      copyBtn.style.padding = '3px 8px';
      copyBtn.style.background = 'rgba(0,0,0,0.1)';
      copyBtn.style.border = 'none';
      copyBtn.style.borderRadius = '4px';
      copyBtn.style.fontSize = '12px';
      copyBtn.style.cursor = 'pointer';
      pre.appendChild(copyBtn);
    });
    
    // 为表格添加样式
    contentDiv.querySelectorAll('table').forEach((table) => {
      table.style.width = '100%';
      table.style.borderCollapse = 'collapse';
      table.style.marginBottom = '16px';
      table.style.overflow = 'hidden';
      table.style.borderRadius = '8px';
      table.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.05)';
      
      // 美化表头
      const headers = table.querySelectorAll('th');
      headers.forEach(header => {
        header.style.backgroundColor = '#2563EB';
        header.style.color = 'white';
        header.style.padding = '10px 16px';
        header.style.textAlign = 'left';
        header.style.fontWeight = '600';
      });
      
      // 美化表格单元格
      const cells = table.querySelectorAll('td');
      cells.forEach(cell => {
        cell.style.padding = '10px 16px';
        cell.style.borderTop = '1px solid #E2E8F0';
      });
      
      // 添加斑马纹
      const rows = table.querySelectorAll('tr:not(:first-child)');
      rows.forEach((row, index) => {
        if (index % 2 === 0) {
          row.style.backgroundColor = '#F8FAFC';
        }
      });
    });
  }

  container.appendChild(messageDiv);

  // 滚动到底部
  container.scrollTop = container.scrollHeight;
  
  // 触发进场动画
  setTimeout(() => {
    messageDiv.style.transition = 'all 0.5s ease';
    messageDiv.style.opacity = '1';
    messageDiv.style.transform = 'translateY(0)';
  }, 10);
}

async function showLoading() {
  isLoading = true;
  const sendBtn = document.getElementById('send-btn');
  sendBtn.disabled = true;
  sendBtn.textContent = '发送中...';

  const container = document.getElementById('messages-container');
  
  // 创建骨架屏
  const loadingDiv = document.createElement('div');
  loadingDiv.className = 'message-item message-ai';
  loadingDiv.id = 'loading-message';
  
  // 添加头像
  const avatarImg = document.createElement('img');
  avatarImg.className = 'message-avatar';
  const agentConfigs = await getAgentConfigsCached();
  const agentConfig = agentConfigs[currentAgent.name];
  avatarImg.src = agentConfig ? agentConfig.avatar : 'touxiang/11.png';
  avatarImg.alt = `${currentAgent.name} 头像`;
  
  // 创建消息内容容器
  const contentDiv = document.createElement('div');
  contentDiv.className = 'message-content';
  
  // 添加骨架屏和加载动画
  contentDiv.innerHTML = `
    <div class="markdown-loading-skeleton">
      <div class="skeleton title"></div>
      <div class="skeleton line"></div>
      <div class="skeleton line"></div>
      <div class="skeleton line short"></div>
      <div class="skeleton line"></div>
    </div>
    <div class="loader-box">
      <div class="dot-loader">
        <span>.</span><span>.</span><span>.</span>
      </div>
      <div class="tip">AI 正在生成内容，请稍候...</div>
    </div>
  `;
  
  // 添加到消息项
  loadingDiv.appendChild(avatarImg);
  loadingDiv.appendChild(contentDiv);

  container.appendChild(loadingDiv);
  container.scrollTop = container.scrollHeight;
  
  // 定义多条加载提示信息
  const loadingTips = [
    'AI 正在思考问题...',
    'AI 正在提取关键信息...',
    'AI 正在组织语言结构...',
    'AI 正在整理相关知识...',
    'AI 正在生成回答...',
    'AI 正在优化表达方式...',
    'AI 正在检查回答准确性...',
    'AI 正在完善细节...',
    'AI 正在润色表达...',
    'AI 马上就好...'
  ];
  
  // 设置定时器，每2秒轮换一次提示信息
  let tipIndex = 0;
  const tipRotationInterval = setInterval(() => {
    if (!isLoading) {
      clearInterval(tipRotationInterval);
      return;
    }
    
    tipIndex = (tipIndex + 1) % loadingTips.length;
    const loadingMessage = document.getElementById('loading-message');
    if (loadingMessage) {
      const tipElement = loadingMessage.querySelector('.tip');
      if (tipElement) {
        // 添加淡出淡入效果
        tipElement.style.opacity = '0';
        setTimeout(() => {
          tipElement.textContent = loadingTips[tipIndex];
          tipElement.style.opacity = '1';
        }, 300);
      }
    }
  }, 2000);
  
  // 当组件卸载时清除定时器
  window.loadingTipInterval = tipRotationInterval;
}

function hideLoading() {
  isLoading = false;
  const sendBtn = document.getElementById('send-btn');
  sendBtn.disabled = false;
  sendBtn.textContent = '发送消息';

  // 添加淡出效果
  const loadingDiv = document.getElementById('loading-message');
  if (loadingDiv) {
    loadingDiv.style.transition = 'opacity 0.3s ease';
    loadingDiv.style.opacity = '0';
    
    setTimeout(() => {
      loadingDiv.remove();
    }, 300);
  }
}

// 调用真实的 AI API
async function callRealAI(userMessage) {
  try {
    // 根据智能体类型添加角色提示
    const rolePrompts = {
      'Coze助手': '',
      '智能客服': '你是一个专业的智能客服，专注于解决用户问题，提供清晰的解决方案。',
      '学习助手': '你是一个学习指导专家，擅长制定学习计划，解释复杂概念，提供学习建议。',
      '编程助手': '你是一个编程专家，擅长提供代码示例，解释技术概念，帮助解决编程问题。'
    };

    const rolePrompt = rolePrompts[currentAgent.name] || '';
    const finalMessage = rolePrompt ? `${rolePrompt}\n\n用户问题：${userMessage}` : userMessage;

    // 调用 Coze API
    const response = await callCozeAPI(finalMessage, currentAgent.bot_id);

    hideLoading();
    await addMessage(response, 'ai');

  } catch (error) {
    console.error('AI 调用失败:', error);
    hideLoading();

    const errorMessage = `# ❌ 调用失败

很抱歉，AI 服务暂时不可用。

## 错误信息
${error.message}

## 建议
- 请检查网络连接
- 稍后再试
- 如果问题持续存在，请联系技术支持

---
*错误时间: ${new Date().toLocaleString()}*`;

    // 添加带有视觉反馈的错误消息
    const container = document.getElementById('messages-container');
    const errorDiv = document.createElement('div');
    errorDiv.className = 'message-item message-ai';
    
    // 使用 error-message 样式包装错误内容
    const html = marked.parse(errorMessage);
    errorDiv.innerHTML = `
      <div class="error-message">
        <div class="markdown-body">${html}</div>
      </div>
    `;
    
    container.appendChild(errorDiv);
    container.scrollTop = container.scrollHeight;
    
    // 添加轻微震动效果提示用户出错
    container.classList.add('shake-error');
    setTimeout(() => {
      container.classList.remove('shake-error');
    }, 500);
  }
}

// 检查 API 连接状态
async function checkAPIStatus() {
  try {
    console.log('检查 Coze API 连接状态...');

    // 发送一个简单的测试请求
    const testResponse = await callCozeAPI('你好', COZE_CONFIG.bot_id);

    if (testResponse && !testResponse.includes('❌')) {
      console.log('✅ Coze API 连接正常');
      return true;
    } else {
      console.warn('⚠️ Coze API 响应异常');
      return false;
    }
  } catch (error) {
    console.error('❌ Coze API 连接失败:', error);
    return false;
  }
}

// 显示 API 状态
async function showAPIStatus(isConnected) {
  const container = document.getElementById('messages-container');

  if (isConnected) {
    const welcomeMessage = `# 🎉 欢迎使用 Coze AI 聊天

## ✅ 系统状态
- **API 连接**: 正常
- **Bot ID**: ${COZE_CONFIG.bot_id}
- **Markdown 渲染**: 已启用
- **代码高亮**: 已启用

## 🚀 开始使用
选择左侧的智能体，开始您的 AI 对话之旅！

---
*系统初始化完成 - ${new Date().toLocaleString()}*`;

    await addMessage(welcomeMessage, 'ai');
  } else {
    const errorMessage = `# ⚠️ 系统初始化

## ❌ API 连接状态
连接到 Coze API 时遇到问题。

## 🔧 可能的原因
- **网络连接问题**: 请检查网络连接
- **Token 无效**: 请检查 SAT Token 是否正确
- **Bot ID 错误**: 请确认 Bot ID 是否有效
- **权限不足**: 请检查 Token 权限

## 💡 建议
1. 刷新页面重试
2. 检查控制台错误信息
3. 联系技术支持

---
*您仍然可以查看界面功能，但无法进行真实对话*`;

    await addMessage(errorMessage, 'ai');
  }
}

// 动态生成智能体列表
async function generateAgentList() {
  console.log('开始生成智能体列表...');

  const agentList = document.getElementById('agent-list');
  agentList.innerHTML = '';

  try {
    // 重置缓存，确保获取最新配置
    resetAgentConfigsCache();

    // 获取智能体配置
    const agentConfigs = await getAgentConfigsCached();
    console.log('获取到智能体配置:', agentConfigs);

    const agents = Object.entries(agentConfigs);
    console.log('智能体列表:', agents);

    // 生成智能体列表项
    generateAgentListItems(agents, agentList);

    console.log('智能体列表生成完成');
  } catch (error) {
    console.error('生成智能体列表失败:', error);
    showEmptyAgentMessage(agentList);
  }
}

// 生成智能体列表项
function generateAgentListItems(agents, agentList) {
  // 分类智能体
  const employeeAgents = agents.filter(([_, config]) => config.type === 'employee');
  const toolAgents = agents.filter(([_, config]) => config.type === 'tool');

  // 如果有员工类型的智能体，先添加一个分类标题
  if (employeeAgents.length > 0) {
    const employeeTitle = document.createElement('div');
    employeeTitle.className = 'agent-category-title';
    employeeTitle.textContent = '媒体部';
    agentList.appendChild(employeeTitle);

    // 添加员工智能体
    employeeAgents.forEach(([name, config]) => {
      const agentItem = createAgentItem(name, config);
      agentList.appendChild(agentItem);
    });
  }

  // 如果有工具类型的智能体，添加一个分类标题
  if (toolAgents.length > 0) {
    const toolTitle = document.createElement('div');
    toolTitle.className = 'agent-category-title';
    toolTitle.textContent = '行政部';
    agentList.appendChild(toolTitle);

    // 添加工具智能体
    toolAgents.forEach(([name, config]) => {
      const agentItem = createAgentItem(name, config);
      agentList.appendChild(agentItem);
    });
  }

  console.log('智能体列表生成完成');
}

// 显示空智能体消息
function showEmptyAgentMessage(agentList) {
  const emptyMessage = document.createElement('div');
  emptyMessage.className = 'empty-agents-message';
  emptyMessage.innerHTML = `
    <p>智能体配置加载失败</p>
    <p>正在使用默认智能体...</p>
  `;
  agentList.appendChild(emptyMessage);
}

// 创建智能体列表项
function createAgentItem(name, config) {
  console.log('创建智能体项:', name, config);

  const agentItem = document.createElement('div');
  agentItem.className = 'agent-item';

  // 只有在有当前智能体且名称匹配时才添加选中状态
  if (currentAgent.name && name === currentAgent.name) {
    agentItem.classList.add('selected');
  }

  agentItem.innerHTML = `
    <img src="${config.avatar || 'touxiang/11.png'}" class="avatar" />
    <div class="agent-info">
      <div class="name">${name}</div>
      <div class="desc">${config.description || '智能助手'}</div>
    </div>
  `;

  agentItem.addEventListener('click', function() {
    selectAgent(this, name);
  });

  console.log('智能体项创建完成:', agentItem);
  return agentItem;
}

// 页面加载完成后初始化
window.onload = async () => {
  console.log('页面开始初始化...');

  // 检查依赖库
  if (typeof marked === 'undefined') {
    console.warn('Marked.js 未加载，Markdown 渲染功能可能不可用');
  }

  if (typeof hljs === 'undefined') {
    console.warn('Highlight.js 未加载，代码高亮功能可能不可用');
  }

  if (typeof CozeWebSDK === 'undefined') {
    console.warn('Coze SDK 未加载，但我们使用直接 API 调用');
  }

  // 添加页面进场动画
  document.body.style.opacity = '0';
  document.body.style.transform = 'translateY(20px)';
  document.body.style.transition = 'all 0.6s ease-out';

  setTimeout(() => {
    document.body.style.opacity = '1';
    document.body.style.transform = 'translateY(0)';
  }, 100);

  // 初始化智能体配置
  console.log('正在初始化智能体配置...');

  // 重置缓存，确保获取最新配置
  resetAgentConfigsCache();

  // 动态生成智能体列表
  await generateAgentList();

  // 等待DOM更新后再选择智能体
  setTimeout(async () => {
    const firstAgentItem = document.querySelector('.agent-item');
    console.log('查找智能体项:', firstAgentItem);

    if (firstAgentItem) {
      const nameElement = firstAgentItem.querySelector('.name');
      if (nameElement) {
        const agentName = nameElement.textContent;
        console.log('自动选择智能体:', agentName);
        await selectAgent(firstAgentItem, agentName);
      } else {
        console.error('找到智能体项但没有找到名称元素');
        await showDefaultAgent();
      }
    } else {
      console.warn('没有找到智能体项，显示默认智能体');
      // 如果仍然没有智能体，强制显示默认智能体
      await showDefaultAgent();
    }
  }, 500); // 增加等待时间

  // 聚焦输入框
  document.getElementById('user-input').focus();

  console.log('页面初始化完成');
};

async function getAgentWelcomeMessage(agentName) {
  try {
    // 从配置中获取欢迎消息
    const agentConfigs = await getAgentConfigsCached();
    const agentConfig = agentConfigs[agentName];

    if (agentConfig && agentConfig.greeting) {
      return agentConfig.greeting;
    }
  } catch (error) {
    console.warn('获取欢迎消息失败:', error);
  }

  // 如果没有找到配置，返回默认消息
  return `👋 您好！我是 ${agentName}\n\n我已经准备好为您服务了！`;
}

// 显示默认智能体（当没有配置时的备用方案）
async function showDefaultAgent() {
  console.log('显示默认智能体');

  // 设置默认智能体信息
  currentAgent.name = 'Coze助手';
  currentAgent.bot_id = '7523880023146168366';

  document.getElementById('current-agent').textContent = 'Coze助手';
  document.getElementById('chat-agent-name').textContent = 'Coze助手';

  // 清空聊天容器
  const container = document.getElementById('messages-container');
  container.innerHTML = '';

  // 初始化聊天记录
  if (!chatHistories['Coze助手']) {
    chatHistories['Coze助手'] = [];
  }

  // 显示欢迎消息
  const welcomeMessage = await getAgentWelcomeMessage('Coze助手');
  await displayMessage(welcomeMessage, 'ai', container);

  // 保存欢迎消息到历史记录
  chatHistories['Coze助手'].push({
    content: welcomeMessage,
    type: 'ai',
    timestamp: new Date().getTime()
  });

  // 强制生成一个默认的智能体列表项
  const agentList = document.getElementById('agent-list');
  agentList.innerHTML = `
    <div class="agent-item selected" onclick="selectAgent(this, 'Coze助手')">
      <img src="touxiang/11.png" class="avatar" />
      <div class="agent-info">
        <div class="name">Coze助手</div>
        <div class="desc">智能AI助手，为您答疑解惑</div>
      </div>
    </div>
  `;
}


