// Coze API 配置
const COZE_CONFIG = {
  bot_id: '7523880023146168366',
  token: 'sat_sWfwyuKNho8ZHtkm2nmiXsXlFntAptsaa9XC6HpofAA3XLpTRoS5gGmkCJ2LpwaP',
  api_base: 'https://api.coze.cn/open_api/v2',
};

let currentAgent = {
  name: '',
  bot_id: COZE_CONFIG.bot_id
};
let isLoading = false;
let conversationId = null; // 用于维持对话上下文

// 存储所有智能体的聊天记录
let chatHistories = {};

// 处理网络地址的函数
function processNetworkUrls(content) {
  // 图片文件扩展名
  const imageExtensions = /\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i;
  // 下载文件扩展名
  const downloadExtensions = /\.(pdf|doc|docx|xls|xlsx|ppt|pptx|zip|rar|7z|tar|gz|mp4|mp3|avi|mov|txt|csv)$/i;

  // 先处理已经是 Markdown 格式的链接，避免重复处理
  const markdownLinkRegex = /\[([^\]]*)\]\(([^)]+)\)/g;
  const existingLinks = [];
  content.replace(markdownLinkRegex, (match, text, url) => {
    existingLinks.push(url);
    return match;
  });

  // URL 正则表达式 - 匹配不在 Markdown 链接中的 URL
  const urlRegex = /(https?:\/\/[^\s<>"{}|\\^`\[\]()]+)/g;

  return content.replace(urlRegex, (url) => {
    // 移除末尾的标点符号
    const cleanUrl = url.replace(/[.,;:!?]+$/, '');

    // 如果这个 URL 已经在 Markdown 链接中，跳过处理
    if (existingLinks.includes(cleanUrl)) {
      return url;
    }

    if (imageExtensions.test(cleanUrl)) {
      // 如果是图片地址，生成图片标签
      return `![图片](${cleanUrl})`;
    } else if (downloadExtensions.test(cleanUrl)) {
      // 如果是下载文件地址，生成下载按钮
      const fileName = cleanUrl.split('/').pop() || '下载文件';
      return `[📥 下载 ${fileName}](${cleanUrl})`;
    } else {
      // 其他网络地址保持原样，但转换为链接格式
      return `[🔗 ${cleanUrl}](${cleanUrl})`;
    }
  });
}

// Markdown 渲染配置
if (typeof marked !== 'undefined') {
  marked.setOptions({
    highlight: function(code, lang) {
      if (typeof hljs !== 'undefined' && lang && hljs.getLanguage(lang)) {
        try {
          return hljs.highlight(code, { language: lang }).value;
        } catch (err) {}
      }
      return code;
    },
    breaks: true,
    gfm: true
  });
}

// 调用 Coze API
async function callCozeAPI(message, botId = COZE_CONFIG.bot_id) {
  try {
    console.log('调用 Coze API:', { message, botId, conversationId });

    const requestBody = {
      bot_id: botId,
      user: "user_" + Date.now(),
      query: message,
      chat_history: [],
      stream: false // 禁用流式输出
    };

    // 如果有对话ID，添加到请求中
    if (conversationId) {
      requestBody.conversation_id = conversationId;
    }

    const response = await fetch(`${COZE_CONFIG.api_base}/chat`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${COZE_CONFIG.token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`API 调用失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('API 响应:', data);

    // 保存对话ID用于后续对话
    if (data.conversation_id) {
      conversationId = data.conversation_id;
    }

    // 提取回复内容
    if (data.messages && data.messages.length > 0) {
      // 找到最后一条 assistant 消息
      const assistantMessage = data.messages
        .filter(msg => msg.role === 'assistant' && msg.type === 'answer')
        .pop();

      if (assistantMessage && assistantMessage.content) {
        return assistantMessage.content;
      }
    }

    // 如果没有找到合适的回复，返回默认消息
    return '抱歉，我暂时无法回答您的问题，请稍后再试。';

  } catch (error) {
    console.error('Coze API 调用错误:', error);

    // 根据错误类型返回不同的错误消息
    if (error.message.includes('401')) {
      return '❌ **认证失败**\n\n请检查 API Token 是否正确。';
    } else if (error.message.includes('403')) {
      return '❌ **权限不足**\n\n请检查 Token 权限或 Bot ID 是否正确。';
    } else if (error.message.includes('429')) {
      return '❌ **请求过于频繁**\n\n请稍后再试。';
    } else if (error.message.includes('500')) {
      return '❌ **服务器错误**\n\n服务暂时不可用，请稍后再试。';
    } else {
      return `❌ **网络错误**\n\n${error.message}\n\n请检查网络连接后重试。`;
    }
  }
}

// Markdown 渲染配置
if (typeof marked !== 'undefined') {
  marked.setOptions({
    highlight: function(code, lang) {
      if (typeof hljs !== 'undefined' && lang && hljs.getLanguage(lang)) {
        try {
          return hljs.highlight(code, { language: lang }).value;
        } catch (err) {}
      }
      return code;
    },
    breaks: true,
    gfm: true
  });
}

// 从本地存储获取智能体配置
function getAgentConfigs() {
  const STORAGE_KEY = 'coze_agents_config';
  console.log('获取智能体配置...');

  // 默认配置
  const defaultAgents = [
    {
      id: generateId(),
      name: 'Coze助手',
      description: '智能AI助手，为您答疑解惑',
      type: 'employee',
      bot_id: '7523880023146168366',
      avatar: 'touxiang/11.png'
    },
    {
      id: generateId(),
      name: '智能客服',
      description: '专业的客服助手',
      type: 'employee',
      bot_id: '7523880023146168366',
      avatar: 'touxiang/12.png'
    },
    {
      id: generateId(),
      name: '学习助手',
      description: '帮助您学习和成长',
      type: 'tool',
      bot_id: '7523880023146168366',
      avatar: 'touxiang/13.png'
    },
    {
      id: generateId(),
      name: '编程助手',
      description: '代码编写和技术问题',
      type: 'tool',
      bot_id: '7523880023146168366',
      avatar: 'touxiang/16.png'
    }
  ];

  let agents = [];

  try {
    const agentsJson = localStorage.getItem(STORAGE_KEY);
    if (agentsJson) {
      agents = JSON.parse(agentsJson);
      console.log('从 localStorage 读取到配置:', agents);
    }
  } catch (error) {
    console.warn('读取智能体配置失败，使用默认配置:', error);
    agents = [];
  }

  // 如果没有配置或配置为空，使用默认配置
  if (!agents || agents.length === 0) {
    console.log('使用默认配置');
    agents = defaultAgents;

    // 保存默认配置到 localStorage
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(agents));
      console.log('已保存默认智能体配置到 localStorage');
    } catch (error) {
      console.warn('保存智能体配置失败:', error);
    }
  }

  // 转换为所需的格式
  const configs = {};
  agents.forEach(agent => {
    configs[agent.name] = {
      bot_id: agent.bot_id,
      description: agent.description,
      avatar: agent.avatar,
      type: agent.type
    };
  });

  console.log('智能体配置已加载:', configs);
  return configs;
}

// 生成唯一ID
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
}

// 智能体配置 - 动态获取，避免初始化时的问题
let AGENT_CONFIGS = null;

// 获取智能体配置（带缓存）
function getAgentConfigsCached() {
  if (!AGENT_CONFIGS) {
    AGENT_CONFIGS = getAgentConfigs();
  }
  return AGENT_CONFIGS;
}

// 重置智能体配置缓存
function resetAgentConfigsCache() {
  AGENT_CONFIGS = null;
}

// 智能体选择功能
function selectAgent(element, agentName) {
  // 移除所有选中状态
  document.querySelectorAll('.agent-item').forEach(item => {
    item.classList.remove('selected');
  });

  // 添加选中状态
  element.classList.add('selected');

  // 更新当前智能体信息
  const agentConfigs = getAgentConfigsCached();
  const agentConfig = agentConfigs[agentName] || agentConfigs['Coze助手'];
  currentAgent.name = agentName;
  currentAgent.bot_id = agentConfig.bot_id;

  document.getElementById('current-agent').textContent = agentName;
  document.getElementById('chat-agent-name').textContent = agentName;

  // 重置对话ID（切换智能体时开始新对话）
  conversationId = null;

  // 清空聊天容器，准备显示历史记录
  const container = document.getElementById('messages-container');
  container.innerHTML = '';

  // 初始化该智能体的聊天记录（如果不存在）
  if (!chatHistories[agentName]) {
    chatHistories[agentName] = [];
  }

  // 检查是否有历史聊天记录
  if (chatHistories[agentName].length === 0) {
    // 如果没有历史记录，显示欢迎消息
    const welcomeMessage = getAgentWelcomeMessage(agentName);
    
    // 直接添加到DOM，不保存到历史记录中
    displayMessage(welcomeMessage, 'ai', container);
    
    // 保存欢迎消息到历史记录
    chatHistories[agentName].push({
      content: welcomeMessage,
      type: 'ai',
      timestamp: new Date().getTime()
    });
  } else {
    // 如果有历史记录，显示所有历史消息
    chatHistories[agentName].forEach(msg => {
      displayMessage(msg.content, msg.type, container);
    });
  }

  console.log(`切换到智能体: ${agentName} (Bot ID: ${agentConfig.bot_id})`);
}

// 显示消息到界面，但不保存到历史记录
function displayMessage(content, type, container) {
  const messageDiv = document.createElement('div');
  messageDiv.className = `message-item message-${type}`;
  
  // 添加头像
  const avatarImg = document.createElement('img');
  avatarImg.className = 'message-avatar';
  
  if (type === 'user') {
    // 用户头像 - 使用输入区域的头像
    avatarImg.src = document.querySelector('.user-avatar').src;
    avatarImg.alt = '用户头像';
    
    // 创建消息内容容器
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.textContent = content;
    
    // 添加到消息项
    messageDiv.appendChild(avatarImg);
    messageDiv.appendChild(contentDiv);
  } else {
    // AI 头像 - 使用当前智能体的头像
    const agentConfigs = getAgentConfigsCached();
    const agentConfig = agentConfigs[currentAgent.name];
    avatarImg.src = agentConfig ? agentConfig.avatar : 'touxiang/11.png';
    avatarImg.alt = `${currentAgent.name} 头像`;
    
    // 创建消息内容容器
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    
    // AI 消息，渲染 Markdown 并处理网络地址
    const processedContent = processNetworkUrls(content);
    const html = marked.parse(processedContent);

    // 使用新的 markdown-container 类包装内容
    contentDiv.innerHTML = `<div class="markdown-container markdown-body">${html}</div>`;
    
    // 添加到消息项
    messageDiv.appendChild(avatarImg);
    messageDiv.appendChild(contentDiv);

    // 高亮代码块
    contentDiv.querySelectorAll('pre code').forEach((block) => {
      hljs.highlightElement(block);
      
      // 添加代码块复制按钮
      const pre = block.parentElement;
      const copyBtn = document.createElement('button');
      copyBtn.className = 'copy-code-btn';
      copyBtn.innerHTML = '复制';
      copyBtn.onclick = function() {
        const code = block.textContent;
        navigator.clipboard.writeText(code).then(() => {
          copyBtn.innerHTML = '已复制!';
          setTimeout(() => {
            copyBtn.innerHTML = '复制';
          }, 2000);
        });
      };
      pre.style.position = 'relative';
      copyBtn.style.position = 'absolute';
      copyBtn.style.top = '5px';
      copyBtn.style.right = '5px';
      copyBtn.style.padding = '3px 8px';
      copyBtn.style.background = 'rgba(0,0,0,0.1)';
      copyBtn.style.border = 'none';
      copyBtn.style.borderRadius = '4px';
      copyBtn.style.fontSize = '12px';
      copyBtn.style.cursor = 'pointer';
      pre.appendChild(copyBtn);
    });
    
    // 为表格添加样式
    contentDiv.querySelectorAll('table').forEach((table) => {
      table.style.width = '100%';
      table.style.borderCollapse = 'collapse';
      table.style.marginBottom = '16px';
      table.style.overflow = 'hidden';
      table.style.borderRadius = '8px';
      table.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.05)';
      
      // 美化表头
      const headers = table.querySelectorAll('th');
      headers.forEach(header => {
        header.style.backgroundColor = '#2563EB';
        header.style.color = 'white';
        header.style.padding = '10px 16px';
        header.style.textAlign = 'left';
        header.style.fontWeight = '600';
      });
      
      // 美化表格单元格
      const cells = table.querySelectorAll('td');
      cells.forEach(cell => {
        cell.style.padding = '10px 16px';
        cell.style.borderTop = '1px solid #E2E8F0';
      });
      
      // 添加斑马纹
      const rows = table.querySelectorAll('tr:not(:first-child)');
      rows.forEach((row, index) => {
        if (index % 2 === 0) {
          row.style.backgroundColor = '#F8FAFC';
        }
      });
    });
  }

  container.appendChild(messageDiv);

  // 滚动到底部
  container.scrollTop = container.scrollHeight;
}

// 聊天功能
function handleKeyDown(event) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
}

function sendMessage() {
  if (isLoading) return;

  const input = document.getElementById('user-input');
  const message = input.value.trim();

  if (!message) return;

  // 添加用户消息
  addMessage(message, 'user');

  // 清空输入框
  input.value = '';

  // 显示加载状态
  showLoading();

  // 调用真实的 AI API
  callRealAI(message);
}

function addMessage(content, type) {
  const container = document.getElementById('messages-container');
  const messageDiv = document.createElement('div');
  messageDiv.className = `message-item message-${type}`;
  
  // 添加进场动画类
  messageDiv.style.opacity = '0';
  messageDiv.style.transform = 'translateY(20px)';

  // 创建消息对象
  const messageObj = {
    content: content,
    type: type,
    timestamp: new Date().getTime()
  };
  
  // 保存消息到当前智能体的聊天历史
  chatHistories[currentAgent.name].push(messageObj);

  // 添加头像
  const avatarImg = document.createElement('img');
  avatarImg.className = 'message-avatar';
  
  if (type === 'user') {
    // 用户头像 - 使用输入区域的头像
    avatarImg.src = document.querySelector('.user-avatar').src;
    avatarImg.alt = '用户头像';
    
    // 创建消息内容容器
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.textContent = content;
    
    // 添加到消息项
    messageDiv.appendChild(avatarImg);
    messageDiv.appendChild(contentDiv);
  } else {
    // AI 头像 - 使用当前智能体的头像
    const agentConfigs = getAgentConfigsCached();
    const agentConfig = agentConfigs[currentAgent.name];
    avatarImg.src = agentConfig ? agentConfig.avatar : 'touxiang/11.png';
    avatarImg.alt = `${currentAgent.name} 头像`;
    
    // 创建消息内容容器
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    
    // AI 消息，渲染 Markdown 并处理网络地址
    const processedContent = processNetworkUrls(content);
    const html = marked.parse(processedContent);

    // 使用新的 markdown-container 类包装内容
    contentDiv.innerHTML = `<div class="markdown-container markdown-body">${html}</div>`;
    
    // 添加到消息项
    messageDiv.appendChild(avatarImg);
    messageDiv.appendChild(contentDiv);

    // 高亮代码块
    contentDiv.querySelectorAll('pre code').forEach((block) => {
      hljs.highlightElement(block);
      
      // 添加代码块复制按钮
      const pre = block.parentElement;
      const copyBtn = document.createElement('button');
      copyBtn.className = 'copy-code-btn';
      copyBtn.innerHTML = '复制';
      copyBtn.onclick = function() {
        const code = block.textContent;
        navigator.clipboard.writeText(code).then(() => {
          copyBtn.innerHTML = '已复制!';
          setTimeout(() => {
            copyBtn.innerHTML = '复制';
          }, 2000);
        });
      };
      pre.style.position = 'relative';
      copyBtn.style.position = 'absolute';
      copyBtn.style.top = '5px';
      copyBtn.style.right = '5px';
      copyBtn.style.padding = '3px 8px';
      copyBtn.style.background = 'rgba(0,0,0,0.1)';
      copyBtn.style.border = 'none';
      copyBtn.style.borderRadius = '4px';
      copyBtn.style.fontSize = '12px';
      copyBtn.style.cursor = 'pointer';
      pre.appendChild(copyBtn);
    });
    
    // 为表格添加样式
    contentDiv.querySelectorAll('table').forEach((table) => {
      table.style.width = '100%';
      table.style.borderCollapse = 'collapse';
      table.style.marginBottom = '16px';
      table.style.overflow = 'hidden';
      table.style.borderRadius = '8px';
      table.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.05)';
      
      // 美化表头
      const headers = table.querySelectorAll('th');
      headers.forEach(header => {
        header.style.backgroundColor = '#2563EB';
        header.style.color = 'white';
        header.style.padding = '10px 16px';
        header.style.textAlign = 'left';
        header.style.fontWeight = '600';
      });
      
      // 美化表格单元格
      const cells = table.querySelectorAll('td');
      cells.forEach(cell => {
        cell.style.padding = '10px 16px';
        cell.style.borderTop = '1px solid #E2E8F0';
      });
      
      // 添加斑马纹
      const rows = table.querySelectorAll('tr:not(:first-child)');
      rows.forEach((row, index) => {
        if (index % 2 === 0) {
          row.style.backgroundColor = '#F8FAFC';
        }
      });
    });
  }

  container.appendChild(messageDiv);

  // 滚动到底部
  container.scrollTop = container.scrollHeight;
  
  // 触发进场动画
  setTimeout(() => {
    messageDiv.style.transition = 'all 0.5s ease';
    messageDiv.style.opacity = '1';
    messageDiv.style.transform = 'translateY(0)';
  }, 10);
}

function showLoading() {
  isLoading = true;
  const sendBtn = document.getElementById('send-btn');
  sendBtn.disabled = true;
  sendBtn.textContent = '发送中...';

  const container = document.getElementById('messages-container');
  
  // 创建骨架屏
  const loadingDiv = document.createElement('div');
  loadingDiv.className = 'message-item message-ai';
  loadingDiv.id = 'loading-message';
  
  // 添加头像
  const avatarImg = document.createElement('img');
  avatarImg.className = 'message-avatar';
  const agentConfigs = getAgentConfigsCached();
  const agentConfig = agentConfigs[currentAgent.name];
  avatarImg.src = agentConfig ? agentConfig.avatar : 'touxiang/11.png';
  avatarImg.alt = `${currentAgent.name} 头像`;
  
  // 创建消息内容容器
  const contentDiv = document.createElement('div');
  contentDiv.className = 'message-content';
  
  // 添加骨架屏和加载动画
  contentDiv.innerHTML = `
    <div class="markdown-loading-skeleton">
      <div class="skeleton title"></div>
      <div class="skeleton line"></div>
      <div class="skeleton line"></div>
      <div class="skeleton line short"></div>
      <div class="skeleton line"></div>
    </div>
    <div class="loader-box">
      <div class="dot-loader">
        <span>.</span><span>.</span><span>.</span>
      </div>
      <div class="tip">AI 正在生成内容，请稍候...</div>
    </div>
  `;
  
  // 添加到消息项
  loadingDiv.appendChild(avatarImg);
  loadingDiv.appendChild(contentDiv);

  container.appendChild(loadingDiv);
  container.scrollTop = container.scrollHeight;
  
  // 定义多条加载提示信息
  const loadingTips = [
    'AI 正在思考问题...',
    'AI 正在提取关键信息...',
    'AI 正在组织语言结构...',
    'AI 正在整理相关知识...',
    'AI 正在生成回答...',
    'AI 正在优化表达方式...',
    'AI 正在检查回答准确性...',
    'AI 正在完善细节...',
    'AI 正在润色表达...',
    'AI 马上就好...'
  ];
  
  // 设置定时器，每2秒轮换一次提示信息
  let tipIndex = 0;
  const tipRotationInterval = setInterval(() => {
    if (!isLoading) {
      clearInterval(tipRotationInterval);
      return;
    }
    
    tipIndex = (tipIndex + 1) % loadingTips.length;
    const loadingMessage = document.getElementById('loading-message');
    if (loadingMessage) {
      const tipElement = loadingMessage.querySelector('.tip');
      if (tipElement) {
        // 添加淡出淡入效果
        tipElement.style.opacity = '0';
        setTimeout(() => {
          tipElement.textContent = loadingTips[tipIndex];
          tipElement.style.opacity = '1';
        }, 300);
      }
    }
  }, 2000);
  
  // 当组件卸载时清除定时器
  window.loadingTipInterval = tipRotationInterval;
}

function hideLoading() {
  isLoading = false;
  const sendBtn = document.getElementById('send-btn');
  sendBtn.disabled = false;
  sendBtn.textContent = '发送消息';

  // 添加淡出效果
  const loadingDiv = document.getElementById('loading-message');
  if (loadingDiv) {
    loadingDiv.style.transition = 'opacity 0.3s ease';
    loadingDiv.style.opacity = '0';
    
    setTimeout(() => {
      loadingDiv.remove();
    }, 300);
  }
}

// 调用真实的 AI API
async function callRealAI(userMessage) {
  try {
    // 根据智能体类型添加角色提示
    const rolePrompts = {
      'Coze助手': '',
      '智能客服': '你是一个专业的智能客服，专注于解决用户问题，提供清晰的解决方案。',
      '学习助手': '你是一个学习指导专家，擅长制定学习计划，解释复杂概念，提供学习建议。',
      '编程助手': '你是一个编程专家，擅长提供代码示例，解释技术概念，帮助解决编程问题。'
    };

    const rolePrompt = rolePrompts[currentAgent.name] || '';
    const finalMessage = rolePrompt ? `${rolePrompt}\n\n用户问题：${userMessage}` : userMessage;

    // 调用 Coze API
    const response = await callCozeAPI(finalMessage, currentAgent.bot_id);

    hideLoading();
    addMessage(response, 'ai');

  } catch (error) {
    console.error('AI 调用失败:', error);
    hideLoading();

    const errorMessage = `# ❌ 调用失败

很抱歉，AI 服务暂时不可用。

## 错误信息
${error.message}

## 建议
- 请检查网络连接
- 稍后再试
- 如果问题持续存在，请联系技术支持

---
*错误时间: ${new Date().toLocaleString()}*`;

    // 添加带有视觉反馈的错误消息
    const container = document.getElementById('messages-container');
    const errorDiv = document.createElement('div');
    errorDiv.className = 'message-item message-ai';
    
    // 使用 error-message 样式包装错误内容
    const html = marked.parse(errorMessage);
    errorDiv.innerHTML = `
      <div class="error-message">
        <div class="markdown-body">${html}</div>
      </div>
    `;
    
    container.appendChild(errorDiv);
    container.scrollTop = container.scrollHeight;
    
    // 添加轻微震动效果提示用户出错
    container.classList.add('shake-error');
    setTimeout(() => {
      container.classList.remove('shake-error');
    }, 500);
  }
}

// 检查 API 连接状态
async function checkAPIStatus() {
  try {
    console.log('检查 Coze API 连接状态...');

    // 发送一个简单的测试请求
    const testResponse = await callCozeAPI('你好', COZE_CONFIG.bot_id);

    if (testResponse && !testResponse.includes('❌')) {
      console.log('✅ Coze API 连接正常');
      return true;
    } else {
      console.warn('⚠️ Coze API 响应异常');
      return false;
    }
  } catch (error) {
    console.error('❌ Coze API 连接失败:', error);
    return false;
  }
}

// 显示 API 状态
function showAPIStatus(isConnected) {
  const container = document.getElementById('messages-container');

  if (isConnected) {
    const welcomeMessage = `# 🎉 欢迎使用 Coze AI 聊天

## ✅ 系统状态
- **API 连接**: 正常
- **Bot ID**: ${COZE_CONFIG.bot_id}
- **Markdown 渲染**: 已启用
- **代码高亮**: 已启用

## 🚀 开始使用
选择左侧的智能体，开始您的 AI 对话之旅！

---
*系统初始化完成 - ${new Date().toLocaleString()}*`;

    addMessage(welcomeMessage, 'ai');
  } else {
    const errorMessage = `# ⚠️ 系统初始化

## ❌ API 连接状态
连接到 Coze API 时遇到问题。

## 🔧 可能的原因
- **网络连接问题**: 请检查网络连接
- **Token 无效**: 请检查 SAT Token 是否正确
- **Bot ID 错误**: 请确认 Bot ID 是否有效
- **权限不足**: 请检查 Token 权限

## 💡 建议
1. 刷新页面重试
2. 检查控制台错误信息
3. 联系技术支持

---
*您仍然可以查看界面功能，但无法进行真实对话*`;

    addMessage(errorMessage, 'ai');
  }
}

// 动态生成智能体列表
function generateAgentList() {
  console.log('开始生成智能体列表...');

  const agentList = document.getElementById('agent-list');
  agentList.innerHTML = '';

  // 直接获取智能体配置
  const agentConfigs = getAgentConfigsCached();
  console.log('获取到智能体配置:', agentConfigs);

  const agents = Object.entries(agentConfigs);
  console.log('智能体列表:', agents);

  // 生成智能体列表项
  generateAgentListItems(agents, agentList);

  console.log('智能体列表生成完成');
}

// 生成智能体列表项
function generateAgentListItems(agents, agentList) {
  // 分类智能体
  const employeeAgents = agents.filter(([_, config]) => config.type === 'employee');
  const toolAgents = agents.filter(([_, config]) => config.type === 'tool');

  // 如果有员工类型的智能体，先添加一个分类标题
  if (employeeAgents.length > 0) {
    const employeeTitle = document.createElement('div');
    employeeTitle.className = 'agent-category-title';
    employeeTitle.textContent = '员工助手';
    agentList.appendChild(employeeTitle);

    // 添加员工智能体
    employeeAgents.forEach(([name, config]) => {
      const agentItem = createAgentItem(name, config);
      agentList.appendChild(agentItem);
    });
  }

  // 如果有工具类型的智能体，添加一个分类标题
  if (toolAgents.length > 0) {
    const toolTitle = document.createElement('div');
    toolTitle.className = 'agent-category-title';
    toolTitle.textContent = '工具助手';
    agentList.appendChild(toolTitle);

    // 添加工具智能体
    toolAgents.forEach(([name, config]) => {
      const agentItem = createAgentItem(name, config);
      agentList.appendChild(agentItem);
    });
  }

  console.log('智能体列表生成完成');
}

// 显示空智能体消息
function showEmptyAgentMessage(agentList) {
  const emptyMessage = document.createElement('div');
  emptyMessage.className = 'empty-agents-message';
  emptyMessage.innerHTML = `
    <p>智能体配置加载失败</p>
    <p>正在使用默认智能体...</p>
  `;
  agentList.appendChild(emptyMessage);
}

// 创建智能体列表项
function createAgentItem(name, config) {
  console.log('创建智能体项:', name, config);

  const agentItem = document.createElement('div');
  agentItem.className = 'agent-item';

  // 只有在有当前智能体且名称匹配时才添加选中状态
  if (currentAgent.name && name === currentAgent.name) {
    agentItem.classList.add('selected');
  }

  agentItem.innerHTML = `
    <img src="${config.avatar || 'touxiang/11.png'}" class="avatar" />
    <div class="agent-info">
      <div class="name">${name}</div>
      <div class="desc">${config.description || '智能助手'}</div>
    </div>
  `;

  agentItem.addEventListener('click', function() {
    selectAgent(this, name);
  });

  console.log('智能体项创建完成:', agentItem);
  return agentItem;
}

// 页面加载完成后初始化
window.onload = async () => {
  console.log('页面开始初始化...');

  // 检查依赖库
  if (typeof marked === 'undefined') {
    console.warn('Marked.js 未加载，Markdown 渲染功能可能不可用');
  }

  if (typeof hljs === 'undefined') {
    console.warn('Highlight.js 未加载，代码高亮功能可能不可用');
  }

  if (typeof CozeWebSDK === 'undefined') {
    console.warn('Coze SDK 未加载，但我们使用直接 API 调用');
  }

  // 添加页面进场动画
  document.body.style.opacity = '0';
  document.body.style.transform = 'translateY(20px)';
  document.body.style.transition = 'all 0.6s ease-out';

  setTimeout(() => {
    document.body.style.opacity = '1';
    document.body.style.transform = 'translateY(0)';
  }, 100);

  // 初始化智能体配置
  console.log('正在初始化智能体配置...');

  // 重置缓存，确保获取最新配置
  resetAgentConfigsCache();

  // 动态生成智能体列表
  generateAgentList();

  // 等待DOM更新后再选择智能体
  setTimeout(() => {
    const firstAgentItem = document.querySelector('.agent-item');
    console.log('查找智能体项:', firstAgentItem);

    if (firstAgentItem) {
      const nameElement = firstAgentItem.querySelector('.name');
      if (nameElement) {
        const agentName = nameElement.textContent;
        console.log('自动选择智能体:', agentName);
        selectAgent(firstAgentItem, agentName);
      } else {
        console.error('找到智能体项但没有找到名称元素');
        showDefaultAgent();
      }
    } else {
      console.warn('没有找到智能体项，显示默认智能体');
      // 如果仍然没有智能体，强制显示默认智能体
      showDefaultAgent();
    }
  }, 500); // 增加等待时间

  // 聚焦输入框
  document.getElementById('user-input').focus();

  console.log('页面初始化完成');
};

function getAgentWelcomeMessage(agentName) {
  const welcomeMessages = {
    'Coze助手': `# 👋 您好！我是 Coze助手

我已经准备好为您服务了！

## 🎯 我的特长

- 🤖 **通用问答**: 回答各种日常问题
- 📚 **知识解答**: 提供准确的信息和解释
- 💡 **创意建议**: 帮助您思考和创新
- 🔍 **信息整理**: 整理和总结复杂信息

## 💡 使用提示

- 您可以用自然语言与我对话
- 我的回复会以美观的 **Markdown 格式** 显示
- 支持代码高亮、表格、列表等丰富格式

请在下方输入您的问题，我会尽力为您解答！`,

    '智能客服': `# 🛠️ 您好！我是智能客服

我专门为您解决各种问题和疑问！

## 🎯 我的特长

- 🛠️ **问题解决**: 快速解决您的疑问
- 📋 **服务指导**: 提供详细的操作指南
- 🔧 **技术支持**: 协助解决技术问题
- 📞 **咨询服务**: 专业的咨询和建议

## 💡 服务承诺

- ⚡ **快速响应**: 及时回复您的问题
- 🎯 **精准解答**: 提供针对性的解决方案
- 📚 **详细指导**: 步骤清晰，易于操作

有什么问题需要我帮助解决吗？`,

    '学习助手': `# 📚 您好！我是学习助手

我来帮助您更好地学习和成长！

## 🎯 我的特长

- 📖 **学习指导**: 制定学习计划和方法
- 🧠 **知识讲解**: 深入浅出地解释概念
- 📝 **作业辅导**: 协助完成学习任务
- 🎓 **考试准备**: 提供复习建议和技巧

## 📈 学习理念

- 🌱 **循序渐进**: 从基础开始，逐步提升
- 🎯 **因材施教**: 根据您的需求定制学习方案
- 💪 **持续改进**: 不断优化学习方法

准备开始学习之旅了吗？告诉我您想学什么！`,

    '编程助手': `# 💻 您好！我是编程助手

我专门帮助您解决编程相关的问题！

## 🎯 我的特长

- 💻 **代码编写**: 帮助编写和优化代码
- 🐛 **调试支持**: 协助查找和修复bug
- 📚 **技术学习**: 解释编程概念和最佳实践
- 🔧 **工具推荐**: 推荐合适的开发工具和框架

## 🚀 支持的技术

- **前端**: HTML, CSS, JavaScript, React, Vue
- **后端**: Node.js, Python, Java, PHP
- **数据库**: MySQL, MongoDB, Redis
- **工具**: Git, Docker, VS Code

有什么编程问题需要我帮助解决吗？`
  };

  return welcomeMessages[agentName] || welcomeMessages['Coze助手'];
}

// 显示默认智能体（当没有配置时的备用方案）
function showDefaultAgent() {
  console.log('显示默认智能体');

  // 设置默认智能体信息
  currentAgent.name = 'Coze助手';
  currentAgent.bot_id = '7523880023146168366';

  document.getElementById('current-agent').textContent = 'Coze助手';
  document.getElementById('chat-agent-name').textContent = 'Coze助手';

  // 清空聊天容器
  const container = document.getElementById('messages-container');
  container.innerHTML = '';

  // 初始化聊天记录
  if (!chatHistories['Coze助手']) {
    chatHistories['Coze助手'] = [];
  }

  // 显示欢迎消息
  const welcomeMessage = getAgentWelcomeMessage('Coze助手');
  displayMessage(welcomeMessage, 'ai', container);

  // 保存欢迎消息到历史记录
  chatHistories['Coze助手'].push({
    content: welcomeMessage,
    type: 'ai',
    timestamp: new Date().getTime()
  });

  // 强制生成一个默认的智能体列表项
  const agentList = document.getElementById('agent-list');
  agentList.innerHTML = `
    <div class="agent-item selected" onclick="selectAgent(this, 'Coze助手')">
      <img src="touxiang/11.png" class="avatar" />
      <div class="agent-info">
        <div class="name">Coze助手</div>
        <div class="desc">智能AI助手，为您答疑解惑</div>
      </div>
    </div>
  `;
}


