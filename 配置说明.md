# 智能体配置系统说明

## 概述

现在智能体配置已经从不稳定的 localStorage 改为基于文件的配置系统，配置数据存储在 `agents-config.json` 文件中，更加稳定可靠。

## 文件结构

### 主要文件
- `agents-config.json` - 智能体配置文件（核心配置）
- `index.html` - 主聊天页面
- `config-manager.html` - 配置管理器（推荐使用）
- `agent-config.html` - 原配置页面（已更新支持文件配置）

## 使用方法

### 1. 查看和编辑配置

**方法一：使用配置管理器（推荐）**
1. 打开 `config-manager.html`
2. 点击"加载当前配置"查看现有配置
3. 在文本框中直接编辑 JSON 配置
4. 点击"保存配置"下载新的配置文件
5. 将下载的 `agents-config.json` 文件放在网站根目录下

**方法二：直接编辑文件**
1. 直接编辑 `agents-config.json` 文件
2. 保存后刷新主页面即可生效

### 2. 配置文件格式

```json
{
  "agents": [
    {
      "id": "agent_001",
      "name": "智能体名称",
      "description": "智能体描述",
      "category": "employee", // 或 "tool"
      "bot_id": "你的Bot ID",
      "avatar": "touxiang/头像文件.png",
      "greeting": "欢迎消息（支持\\n换行）"
    }
  ],
  "categories": [
    {
      "id": "cat_001",
      "name": "分类名称"
    }
  ]
}
```

### 3. 添加新智能体

1. 打开配置管理器
2. 点击"添加示例智能体"
3. 修改智能体信息：
   - `name`: 智能体名称
   - `description`: 描述信息
   - `bot_id`: 你的 Coze Bot ID
   - `avatar`: 头像文件路径
   - `greeting`: 欢迎消息
4. 保存配置

### 4. 修改现有智能体

1. 在配置管理器中加载当前配置
2. 找到要修改的智能体
3. 直接编辑 JSON 内容
4. 验证配置格式
5. 保存配置

## 优势

### 相比 localStorage 的优势：
1. **稳定性** - 不会因为浏览器清理而丢失
2. **可备份** - 可以轻松备份和恢复配置
3. **可版本控制** - 可以用 Git 等工具管理配置变更
4. **可分享** - 可以轻松分享配置给其他人
5. **可编辑** - 可以用任何文本编辑器编辑

### 功能特点：
1. **自动加载** - 页面启动时自动从文件加载配置
2. **容错机制** - 如果文件读取失败，会使用默认配置
3. **实时生效** - 修改配置文件后刷新页面即可生效
4. **格式验证** - 配置管理器提供格式验证功能

## 注意事项

1. **文件位置** - `agents-config.json` 必须放在网站根目录下
2. **JSON 格式** - 配置文件必须是有效的 JSON 格式
3. **字符编码** - 建议使用 UTF-8 编码
4. **备份** - 建议定期备份配置文件

## 故障排除

### 配置不生效
1. 检查 `agents-config.json` 文件是否在正确位置
2. 检查 JSON 格式是否正确
3. 刷新浏览器页面
4. 查看浏览器控制台是否有错误信息

### 配置文件损坏
1. 使用配置管理器的"重置为默认配置"功能
2. 或者删除 `agents-config.json` 文件，系统会使用默认配置

### 无法保存配置
1. 由于浏览器安全限制，无法直接写入文件
2. 使用配置管理器的下载功能
3. 手动将下载的文件放在正确位置

## 示例配置

参考 `agents-config.json` 文件中的示例配置，包含了完整的智能体和分类配置示例。

## 技术说明

- 主页面使用 `fetch()` API 读取配置文件
- 配置管理器提供可视化编辑界面
- 支持异步加载，不会阻塞页面渲染
- 包含完整的错误处理机制
