<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能体配置管理器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h2 {
            margin-top: 0;
            color: #333;
        }
        textarea {
            width: 100%;
            height: 400px;
            font-family: monospace;
            font-size: 12px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-top: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>智能体配置管理器</h1>
        
        <div class="info">
            <strong>使用说明：</strong><br>
            1. 点击"加载当前配置"查看现有配置<br>
            2. 在文本框中编辑配置（JSON格式）<br>
            3. 点击"保存配置"将配置写入 agents-config.json 文件<br>
            4. 刷新主页面即可看到新配置生效
        </div>

        <div class="section">
            <h2>当前配置</h2>
            <button onclick="loadConfig()">加载当前配置</button>
            <button onclick="resetToDefault()">重置为默认配置</button>
            <textarea id="configEditor" placeholder="配置将显示在这里..."></textarea>
            <button onclick="saveConfig()">保存配置</button>
            <button onclick="validateConfig()">验证配置</button>
            <div id="message"></div>
        </div>

        <div class="section">
            <h2>快速操作</h2>
            <button onclick="addSampleAgent()">添加示例智能体</button>
            <button onclick="exportConfig()">导出配置</button>
            <button onclick="importConfig()">导入配置</button>
            <input type="file" id="fileInput" accept=".json" style="display: none;" onchange="handleFileImport(event)">
        </div>
    </div>

    <script>
        let currentConfig = null;

        // 默认配置
        const defaultConfig = {
            "agents": [
                {
                    "id": "agent_001",
                    "name": "Coze助手",
                    "description": "智能AI助手，为您答疑解惑",
                    "category": "cat_001",
                    "bot_id": "7523880023146168366",
                    "avatar": "touxiang/11.png",
                    "greeting": "👋 您好！我是 Coze助手\\n\\n我已经准备好为您服务了！"
                },
                {
                    "id": "agent_002",
                    "name": "智能客服",
                    "description": "专业的客服助手",
                    "category": "cat_001",
                    "bot_id": "7523880023146168366",
                    "avatar": "touxiang/12.png",
                    "greeting": "🛠️ 您好！我是智能客服\\n\\n我专门为您解决各种问题和疑问！"
                },
                {
                    "id": "agent_003",
                    "name": "学习助手",
                    "description": "帮助您学习和成长",
                    "category": "cat_002",
                    "bot_id": "7523880023146168366",
                    "avatar": "touxiang/13.png",
                    "greeting": "📚 您好！我是学习助手\\n\\n我来帮助您更好地学习和成长！"
                },
                {
                    "id": "agent_004",
                    "name": "编程助手",
                    "description": "代码编写和技术问题",
                    "category": "cat_002",
                    "bot_id": "7523880023146168366",
                    "avatar": "touxiang/16.png",
                    "greeting": "💻 您好！我是编程助手\\n\\n我专门帮助您解决编程相关的问题！"
                }
            ],
            "categories": [
                {
                    "id": "cat_001",
                    "name": "员工助手"
                },
                {
                    "id": "cat_002",
                    "name": "工具助手"
                }
            ]
        };

        // 加载配置
        async function loadConfig() {
            try {
                const response = await fetch('agents-config.json');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                currentConfig = await response.json();
                document.getElementById('configEditor').value = JSON.stringify(currentConfig, null, 2);
                showMessage('配置加载成功！', 'success');
            } catch (error) {
                showMessage('加载配置失败: ' + error.message + '，显示默认配置', 'error');
                currentConfig = defaultConfig;
                document.getElementById('configEditor').value = JSON.stringify(defaultConfig, null, 2);
            }
        }

        // 保存配置
        async function saveConfig() {
            try {
                const configText = document.getElementById('configEditor').value;
                const config = JSON.parse(configText);
                
                // 验证配置格式
                if (!config.agents || !Array.isArray(config.agents)) {
                    throw new Error('配置格式错误：缺少 agents 数组');
                }
                
                // 创建下载链接
                const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'agents-config.json';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                currentConfig = config;
                showMessage('配置已导出为 agents-config.json 文件，请将其放在网站根目录下！', 'success');
                
            } catch (error) {
                showMessage('保存配置失败: ' + error.message, 'error');
            }
        }

        // 验证配置
        function validateConfig() {
            try {
                const configText = document.getElementById('configEditor').value;
                const config = JSON.parse(configText);
                
                // 基本验证
                if (!config.agents || !Array.isArray(config.agents)) {
                    throw new Error('缺少 agents 数组');
                }
                
                if (!config.categories || !Array.isArray(config.categories)) {
                    throw new Error('缺少 categories 数组');
                }
                
                // 验证每个智能体
                config.agents.forEach((agent, index) => {
                    if (!agent.name) throw new Error(`智能体 ${index + 1} 缺少名称`);
                    if (!agent.bot_id) throw new Error(`智能体 ${index + 1} 缺少 bot_id`);
                    if (!agent.avatar) throw new Error(`智能体 ${index + 1} 缺少头像`);
                });
                
                showMessage('配置验证通过！', 'success');
                
            } catch (error) {
                showMessage('配置验证失败: ' + error.message, 'error');
            }
        }

        // 重置为默认配置
        function resetToDefault() {
            document.getElementById('configEditor').value = JSON.stringify(defaultConfig, null, 2);
            showMessage('已重置为默认配置', 'success');
        }

        // 添加示例智能体
        function addSampleAgent() {
            try {
                const configText = document.getElementById('configEditor').value;
                let config = configText ? JSON.parse(configText) : { agents: [], categories: [] };
                
                const newAgent = {
                    "id": "agent_" + Date.now(),
                    "name": "新智能体",
                    "description": "请修改描述",
                    "category": "cat_001",
                    "bot_id": "7523880023146168366",
                    "avatar": "touxiang/11.png",
                    "greeting": "您好！我是新的智能体。"
                };
                
                config.agents.push(newAgent);
                document.getElementById('configEditor').value = JSON.stringify(config, null, 2);
                showMessage('已添加示例智能体，请修改相关信息', 'success');
                
            } catch (error) {
                showMessage('添加失败: ' + error.message, 'error');
            }
        }

        // 导出配置
        function exportConfig() {
            const configText = document.getElementById('configEditor').value;
            if (!configText.trim()) {
                showMessage('没有配置可导出', 'error');
                return;
            }
            
            const blob = new Blob([configText], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'agents-config-backup.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            showMessage('配置已导出', 'success');
        }

        // 导入配置
        function importConfig() {
            document.getElementById('fileInput').click();
        }

        // 处理文件导入
        function handleFileImport(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const config = JSON.parse(e.target.result);
                    document.getElementById('configEditor').value = JSON.stringify(config, null, 2);
                    showMessage('配置导入成功', 'success');
                } catch (error) {
                    showMessage('导入失败: ' + error.message, 'error');
                }
            };
            reader.readAsText(file);
        }

        // 显示消息
        function showMessage(message, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = message;
            messageDiv.className = type;
            
            setTimeout(() => {
                messageDiv.textContent = '';
                messageDiv.className = '';
            }, 5000);
        }

        // 页面加载时自动加载配置
        window.onload = function() {
            loadConfig();
        };
    </script>
</body>
</html>
