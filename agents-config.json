{"agents": [{"id": "agent_001", "name": "Coze助手", "description": "智能AI助手，为您答疑解惑", "category": "employee", "bot_id": "7523880023146168366", "avatar": "touxiang/11.png", "greeting": "👋 您好！我是 Coze助手\n\n我已经准备好为您服务了！\n\n## 🎯 我的特长\n\n- 🤖 **通用问答**: 回答各种日常问题\n- 📚 **知识解答**: 提供准确的信息和解释\n- 💡 **创意建议**: 帮助您思考和创新\n- 🔍 **信息整理**: 整理和总结复杂信息\n\n## 💡 使用提示\n\n- 您可以用自然语言与我对话\n- 我的回复会以美观的 **Markdown 格式** 显示\n- 支持代码高亮、表格、列表等丰富格式\n\n请在下方输入您的问题，我会尽力为您解答！"}, {"id": "agent_002", "name": "智能客服", "description": "专业的客服助手", "category": "employee", "bot_id": "7523880023146168366", "avatar": "touxiang/12.png", "greeting": "🛠️ 您好！我是智能客服\n\n我专门为您解决各种问题和疑问！\n\n## 🎯 我的特长\n\n- 🛠️ **问题解决**: 快速解决您的疑问\n- 📋 **服务指导**: 提供详细的操作指南\n- 🔧 **技术支持**: 协助解决技术问题\n- 📞 **咨询服务**: 专业的咨询和建议\n\n## 💡 服务承诺\n\n- ⚡ **快速响应**: 及时回复您的问题\n- 🎯 **精准解答**: 提供针对性的解决方案\n- 📚 **详细指导**: 步骤清晰，易于操作\n\n有什么问题需要我帮助解决吗？"}, {"id": "agent_003", "name": "学习助手", "description": "帮助您学习和成长", "category": "tool", "bot_id": "7523880023146168366", "avatar": "touxiang/13.png", "greeting": "📚 您好！我是学习助手\n\n我来帮助您更好地学习和成长！\n\n## 🎯 我的特长\n\n- 📖 **学习指导**: 制定学习计划和方法\n- 🧠 **知识讲解**: 深入浅出地解释概念\n- 📝 **作业辅导**: 协助完成学习任务\n- 🎓 **考试准备**: 提供复习建议和技巧\n\n## 📈 学习理念\n\n- 🌱 **循序渐进**: 从基础开始，逐步提升\n- 🎯 **因材施教**: 根据您的需求定制学习方案\n- 💪 **持续改进**: 不断优化学习方法\n\n准备开始学习之旅了吗？告诉我您想学什么！"}, {"id": "agent_004", "name": "编程助手", "description": "代码编写和技术问题", "category": "tool", "bot_id": "7523880023146168366", "avatar": "touxiang/16.png", "greeting": "💻 您好！我是编程助手\n\n我专门帮助您解决编程相关的问题！\n\n## 🎯 我的特长\n\n- 💻 **代码编写**: 帮助编写和优化代码\n- 🐛 **调试支持**: 协助查找和修复bug\n- 📚 **技术学习**: 解释编程概念和最佳实践\n- 🔧 **工具推荐**: 推荐合适的开发工具和框架\n\n## 🚀 支持的技术\n\n- **前端**: HTML, CSS, JavaScript, React, Vue\n- **后端**: Node.js, Python, Java, PHP\n- **数据库**: MySQL, MongoDB, Redis\n- **工具**: Git, Docker, VS Code\n\n有什么编程问题需要我帮助解决吗？"}], "categories": [{"id": "cat_001", "name": "员工助手"}, {"id": "cat_002", "name": "工具助手"}]}